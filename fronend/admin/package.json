{"name": "admin", "type": "module", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@vueuse/core": "^13.6.0", "dayjs": "^1.11.13", "element-plus": "^2.10.7", "signature_pad": "^5.0.10", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@antfu/eslint-config": "^5.2.1", "@iconify/vue": "^5.0.0", "@types/node": "^24.2.1", "@unocss/eslint-plugin": "^66.4.2", "@vitejs/plugin-vue": "^6.0.1", "eslint": "^9.33.0", "eslint-plugin-format": "^1.0.1", "sass": "^1.90.0", "terser": "^5.43.1", "typescript": "^5.9.2", "unocss": "^66.4.2", "unplugin-auto-import": "^19.3.0", "unplugin-element-plus": "^0.10.0", "unplugin-vue-components": "^28.8.0", "unplugin-vue-router": "^0.14.0", "vite": "^7.1.2", "vue-tsc": "^3.0.5"}}