import { breakpointsElement, useBreakpoints } from '@vueuse/core'

export function useResponsive() {
  const breakpoints = useBreakpoints(breakpointsElement)

  const isMobile = breakpoints.smaller('md')
  const isTablet = breakpoints.between('md', 'lg')
  const isDesktop = breakpoints.greater('lg')

  // 是否应该折叠侧边栏
  const shouldCollapseSidebar = computed(() => isMobile.value)

  return {
    isMobile,
    isTablet,
    isDesktop,
    shouldCollapseSidebar,
    breakpoints,
  }
}
