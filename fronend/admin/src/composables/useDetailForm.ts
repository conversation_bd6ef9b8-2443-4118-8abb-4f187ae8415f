import type { FormInstance } from 'element-plus'
import { useLoading } from './useLoading'

export interface UseDetailFormOptions<T extends Record<string, any>> {
  /** 初始数据 */
  initData?: T
  /** 获取API */
  getApi?: (id: number) => Promise<T>
  /** 新增API */
  addApi?: (data: T) => Promise<any>
  /** 编辑API */
  editApi?: (id: number, data: T) => Promise<any>
  /** 成功回调 */
  onSuccess?: () => void
  /** 错误回调 */
  onError?: (error: any) => void
  /** 提交前回调，返回false则阻止提交 */
  onBeforeSubmit?: (data: T) => Promise<boolean>
}

export function useDetailForm<T extends Record<string, any>>(options: UseDetailFormOptions<T>) {
  const { initData, getApi, addApi, editApi, onSuccess, onError, onBeforeSubmit } = options

  // 状态管理
  const formRef = ref<FormInstance>()
  const editId = ref<number>(0)
  const visible = ref(false)
  const formSubmiting = ref(false)
  const { loading: contentLoading, withLoading: withContentLoading } = useLoading()
  const formData = reactive({ ...initData }) as T

  // 计算属性
  const isEdit = computed(() => editId.value > 0)

  // 重置表单数据
  function resetFormData(newInitData?: any) {
    Object.assign(formData, newInitData || initData)
  }

  // 打开表单
  async function open(id: number = 0, newInitData?: any) {
    editId.value = id
    formRef.value?.resetFields()
    formSubmiting.value = false
    visible.value = true

    if (id > 0) {
      if (newInitData)
        resetFormData(newInitData)

      try {
        await withContentLoading(async () => {
          const res = await getApi?.(id)
          Object.assign(formData, res?.data)
        })
      }
      catch (error) {
        console.error(error)
        onError?.(error)
      }
    }
    else {
      resetFormData(newInitData)
    }
  }

  // 关闭表单
  function close() {
    visible.value = false
  }

  // 提交表单
  async function submit() {
    if (!formRef.value || formSubmiting.value)
      return

    try {
      formSubmiting.value = true

      await formRef.value?.validate()
      if (onBeforeSubmit && await onBeforeSubmit(formData) === false) {
        formSubmiting.value = false
        return
      }

      if (isEdit.value)
        await editApi?.(editId.value, formData)
      else
        await addApi?.(formData)

      close()
      onSuccess?.()
    }
    catch (error) {
      formSubmiting.value = false
      console.error(error)
      onError?.(error)
    }
  }

  return {
    // 状态
    formRef,
    editId,
    visible,
    formSubmiting,
    contentLoading,
    formData,

    // 计算属性
    isEdit,

    // 方法
    open,
    close,
    submit,
    resetFormData,
  }
}
