import type { RouteLocationNormalized } from 'vue-router'

export interface TabItem {
  name: string
  title: string
  keepAlive: boolean
}

// 数据
const tabs = ref<TabItem[]>([])
const activeTab = ref('')
const keepAlives = computed(() => tabs.value.filter(tab => tab.keepAlive === true).map(tab => tab.name))

export function useTabs() {
  const router = useRouter()

  // 在移除tab时，确保有一个默认的标签页
  const ensureTab = (index: number) => {
    if (tabs.value.length === 0) {
      router.push('/')
      return
    }

    const activeExists = tabs.value.some(tab => tab.name === activeTab.value)
    if (activeExists)
      return

    let newActive: TabItem | null = null

    if (index < tabs.value.length) {
      newActive = tabs.value[index]
    }
    else if (index > 0) {
      newActive = tabs.value[index - 1]
    }
    else {
      newActive = tabs.value[tabs.value.length - 1]
    }

    // 跳转到新激活的标签
    if (newActive) {
      activeTab.value = newActive.name
      router.push(newActive.name)
    }
  }

  // 根据路由添加标签页
  const addTab = (to: RouteLocationNormalized) => {
    const { name, meta } = to
    const title = (meta?.title || name) as string
    const keepAlive = meta?.keepAlive !== false

    if (!tabs.value.some(tab => tab.name === name)) {
      tabs.value.push({ name, title, keepAlive })
    }

    activeTab.value = name
  }

  // 移除标签页
  const removeTab = (name: string) => {
    const index = tabs.value.findIndex(tab => tab.name === name)
    if (index !== -1) {
      tabs.value.splice(index, 1)
      ensureTab(index)
    }
  }

  // 关闭其他标签
  const closeOtherTabs = (name: string) => {
    tabs.value = tabs.value.filter(tab => tab.name === name)
    ensureTab(0)
  }

  // 关闭右侧标签
  const closeRightTabs = (name: string) => {
    const index = tabs.value.findIndex(tab => tab.name === name)
    if (index !== -1) {
      tabs.value = tabs.value.slice(0, index + 1)
      ensureTab(index)
    }
  }

  // 关闭左侧标签
  const closeLeftTabs = (name: string) => {
    const index = tabs.value.findIndex(tab => tab.name === name)
    if (index !== -1) {
      tabs.value = tabs.value.slice(index)
      ensureTab(index)
    }
  }

  // 关闭所有标签
  const closeAllTabs = () => {
    tabs.value = []
    ensureTab(-1)
  }

  // 重置标签数据
  const resetTabs = () => {
    tabs.value = []
    activeTab.value = ''
  }

  return {
    tabs,
    activeTab,
    keepAlives,
    addTab,
    removeTab,
    closeOtherTabs,
    closeRightTabs,
    closeLeftTabs,
    closeAllTabs,
    resetTabs,
  }
}
