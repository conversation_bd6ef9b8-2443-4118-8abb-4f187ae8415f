import type { MenuItem } from '~/utils/router'
import { getRoutesTree } from '~/utils/router'
import { useAuth } from './useAuth'

const { getPerms, hasPerm } = useAuth()

// 全局状态
const allMenu = ref<MenuItem[]>([])
const activeModule = ref<MenuItem | null>(null)

// 根据权限过滤菜单
function filterMenu(menus: MenuItem[]): MenuItem[] {
  return menus.filter((item) => {
    if (item.perm && !hasPerm(item.perm)) {
      return false
    }

    if (item.children && item.children.length > 0) {
      const children = filterMenu(item.children)
      if (children.length === 0 && !item.perm) {
        return false
      }
      item.children = children
    }

    return true
  })
}

// 确保路径以/结尾
function endWithSlash(val: string) {
  return val && val.endsWith('/') ? val : `${val}/`
}

// 设置活跃模块
function setActiveModule(module: MenuItem | null) {
  activeModule.value = module
}

// 根据路由名称查找对应的模块
function getModuleByRoute(routeName: string | null | undefined): MenuItem | null {
  if (!routeName || !allMenu.value.length)
    return allMenu.value?.[0] || null

  return allMenu.value.find(item =>
    endWithSlash(routeName).startsWith(endWithSlash(item.name)),
  ) || allMenu.value[0] || null
}

export function useMenu(watchRoute: boolean = false) {
  const route = useRoute()

  if (watchRoute) {
  // 监听权限变化刷新菜单
    watch(() => getPerms(), (_) => {
      allMenu.value = filterMenu(getRoutesTree())
      // 权限变化后重新设置活跃模块
      if (route.name) {
        activeModule.value = getModuleByRoute(route.name as string)
      }
    }, { immediate: true })

    // 监听路由变化设置菜单选中状态
    watch(
      () => route.name,
      (to) => {
        if (to && to.startsWith(activeModule.value?.name || '#'))
          return

        activeModule.value = getModuleByRoute(to as string)
      },
      { immediate: true },
    )
  }

  return {
    allMenu: readonly(allMenu),
    activeModule: readonly(activeModule),
    setActiveModule,
    filterMenu,
    endWithSlash,
  }
}
