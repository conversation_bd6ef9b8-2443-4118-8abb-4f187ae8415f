export default {
  _name: '系统',
  dept: {
    _name: '科室管理',
    addDept: {
      _name: '新增科室',
    },
    editDept: {
      _name: '编辑科室',
    },
    deleteDept: {
      _name: '删除科室',
    },
    addPost: {
      _name: '新增岗位',
    },
    editPost: {
      _name: '编辑岗位',
    },
    deletePost: {
      _name: '删除岗位',
    },
  },
  user: {
    _name: '账户管理',
    add: {
      _name: '新增账户',
    },
    edit: {
      _name: '编辑账户',
    },
    delete: {
      _name: '删除账户',
    },
    resetPassword: {
      _name: '重置密码',
    },
  },
  perm: {
    _name: '权限管理',
    post: {
      _name: '岗位权限',
    },
    user: {
      _name: '用户权限',
    },
  },
  setting: {
    _name: '系统参数',
    app: {
      _name: '基础设置',
    },
    safe: {
      _name: '安全设置',
    },
    hospitalInfo: {
      _name: '医院信息',
    },
  },
  dict: {
    _name: '数据字典',
    add: {
      _name: '新增',
    },
    edit: {
      _name: '编辑',
    },
    delete: {
      _name: '删除',
    },
    valueEnable: {
      _name: '启用值',
    },
    valueDisable: {
      _name: '禁用值',
    },
    import: {
      _name: '导入',
    },
    export: {
      _name: '导出',
    },
  },
  smsConfig: {
    _name: '短信配置',
    edit: {
      _name: '编辑',
    },
    enable: {
      _name: '禁用',
    },
    disable: {
      _name: '启用',
    },
  },
  barCodeConfig: {
    _name: '条形码配置',
    add: {
      _name: '新增',
    },
    edit: {
      _name: '编辑',
    },
    delete: {
      _name: '删除',
    },
    enable: {
      _name: '禁用',
    },
    disable: {
      _name: '启用',
    },
  },
  idBuilder: {
    _name: '编码配置',
    add: {
      _name: '新增',
    },
    edit: {
      _name: '编辑',
    },
    delete: {
      _name: '删除',
    },
  },
  upfile: {
    _name: '文件管理',
    upload: {
      _name: '上传',
    },
    deletePhysical: {
      _name: '删除',
    },
    download: {
      _name: '下载',
    },
    view: {
      _name: '查看',
    },
  },
  job: {
    _name: '定时任务',
    run: {
      _name: '执行任务',
    },
    enable: {
      _name: '启用任务',
    },
    disable: {
      _name: '禁用任务',
    },
    log: {
      _name: '查看任务日志',
    },
  },
  runtime: {
    _name: '运行信息',
  },
  loginLog: {
    _name: '登录日志',
  },
  auditLog: {
    _name: '审计日志',
    export: {
      _name: '导出',
    },
  },
}
