import type { Directive } from 'vue'

interface OnceClickData {
  enable: () => void
  clickHandler: (event: MouseEvent) => void
}

const cleanupMap = new WeakMap<HTMLElement, OnceClickData>()

export const onceClickDirective: Directive<HTMLElement> = {
  mounted(el, binding) {
    const delay = binding.value ?? 1000
    let timer: ReturnType<typeof setTimeout> | null = null

    const enable = () => {
      if (timer) {
        clearTimeout(timer)
        timer = null
      }
      el.removeAttribute('disabled')
      if ('disabled' in el)
        (el as HTMLButtonElement).disabled = false
    }

    const clickHandler = () => {
      if (el.hasAttribute('disabled'))
        return

      el.setAttribute('disabled', 'disabled')
      if ('disabled' in el)
        (el as HTMLButtonElement).disabled = true

      timer = setTimeout(enable, delay)
    }

    cleanupMap.set(el, { enable, clickHandler })
    el.addEventListener('click', clickHandler)
  },
  unmounted(el) {
    const ref = cleanupMap.get(el)
    if (ref) {
      ref.enable()
      el.removeEventListener('click', ref.clickHandler)
      cleanupMap.delete(el)
    }
  },
}
