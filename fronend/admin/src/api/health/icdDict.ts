import { downloadFile, get, post, upload } from '~/utils/request'

const baseUrl = '/sys/icdDict'

/**
 * 疾病字典列表
 */
export function list(params: any) {
  return get(`${baseUrl}/list`, params)
}

/**
 * 新增疾病字典
 */
export function add(data: any) {
  return post(`${baseUrl}/add`, data)
}

/**
 * 编辑疾病字典(获取)
 */
export function getEdit(id: number) {
  return get(`${baseUrl}/edit`, {
    id,
  })
}

/**
 * 编辑疾病字典(提交)
 */
export function postEdit(id: number, data: any) {
  return post(`${baseUrl}/edit`, data, {
    id,
  })
}

/**
 * 删除疾病字典
 */
export function remove(ids: number[]) {
  return post(`${baseUrl}/delete`, ids)
}

/**
 * 启用疾病字典
 */
export function enable(ids: number[]) {
  return post(`${baseUrl}/enable`, ids)
}

/**
 * 禁用疾病字典
 */
export function disable(ids: number[]) {
  return post(`${baseUrl}/disable`, ids)
}

/**
 * 导出疾病字典
 */
export function exportData() {
  return downloadFile(`${baseUrl}/export`)
}

/**
 * 导入疾病字典
 */
export function importData(file: File) {
  return upload(`${baseUrl}/import`, file)
}
