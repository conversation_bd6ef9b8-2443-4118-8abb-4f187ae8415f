import { download, get, post } from '~/utils/request'

const baseUrl = '/sys/docTemplate'

/**
 * 文书模板列表
 */
export function list(params: any) {
  return get(`${baseUrl}/list`, params)
}

/**
 * 新增文书模板
 */
export function add(data: any) {
  return post(`${baseUrl}/add`, data)
}

/**
 * 编辑文书模板(获取)
 */
export function getEdit(id: number) {
  return get(`${baseUrl}/edit`, {
    id,
  })
}

/**
 * 编辑文书模板(提交)
 */
export function postEdit(id: number, data: any) {
  return post(`${baseUrl}/edit`, data, {
    id,
  })
}

/**
 * 删除文书模板
 */
export function remove(ids: number[]) {
  return post(`${baseUrl}/delete`, ids)
}

/**
 * 启用文书模板
 */
export function enable(ids: number[]) {
  return post(`${baseUrl}/enable`, ids)
}

/**
 * 禁用文书模板
 */
export function disable(ids: number[]) {
  return post(`${baseUrl}/disable`, ids)
}

/**
 * 获取指定模板的模型说明
 */
export function getSchema(code: string): Promise<any> {
  return get<any>(`${baseUrl}/schema`, { code })
}

/**
 * 获取指定分类的文书模板
 */
export function getListByCode(code: string): Promise<any> {
  return get<any>(`${baseUrl}/listByCode`, { code })
}

/**
 * 通过模板生成PDF
 */
export function pdfByTemplate(id: number, data: any) {
  return download(`${baseUrl}/pdfByTemplate`, {
    data,
    params: { id },
  })
}

/**
 * 通过HTML生成PDF
 */
export function pdfByHtml(html: string) {
  return download(`${baseUrl}/pdfByHtml`, { data: html })
}
