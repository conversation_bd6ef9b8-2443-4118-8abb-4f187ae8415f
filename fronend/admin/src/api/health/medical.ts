import { downloadFile, get, post, upload } from '~/utils/request'

const baseUrl = '/sys/medical'

/**
 * 病史类型列表
 */
export function list(params: any) {
  return get(`${baseUrl}/list`, params)
}

/**
 * 新增病史类型
 */
export function add(data: any) {
  return post(`${baseUrl}/add`, data)
}

/**
 * 编辑病史类型(获取)
 */
export function getEdit(id: number) {
  return get(`${baseUrl}/edit`, {
    id,
  })
}

/**
 * 编辑病史类型(提交)
 */
export function postEdit(id: number, data: any) {
  return post(`${baseUrl}/edit`, data, {
    id,
  })
}

/**
 * 删除病史类型
 */
export function remove(ids: number[]) {
  return post(`${baseUrl}/delete`, ids)
}

/**
 * 病史值列表
 */
export function listValue(id: number) {
  return get(`${baseUrl}/valueList`, {
    id,
  })
}

/**
 * 新增病史值
 */
export function addValue(data: any) {
  return post(`${baseUrl}/valueAdd`, data)
}

/**
 * 编辑病史值(获取)
 */
export function getValueEdit(id: number) {
  return get(`${baseUrl}/valueEdit`, {
    id,
  })
}

/**
 * 编辑病史值(提交)
 */
export function postValueEdit(id: number, data: any) {
  return post(`${baseUrl}/valueEdit`, data, {
    id,
  })
}

/**
 * 删除病史值
 */
export function removeValue(ids: number[]) {
  return post(`${baseUrl}/valueDelete`, ids)
}

/**
 * 启用病史值
 */
export function enableValue(ids: number[]) {
  return post(`${baseUrl}/valueEnable`, ids)
}

/**
 * 禁用病史值
 */
export function disableValue(ids: number[]) {
  return post(`${baseUrl}/valueDisable`, ids)
}

/**
 * 导出病史值
 */
export function exportValue(typeId: number) {
  return downloadFile(`${baseUrl}/valueExport`, { params: { typeId } })
}

/**
 * 导入病史值
 */
export function importValue(typeId: number, file: File) {
  return upload(`${baseUrl}/valueImport`, file, { typeId })
}
