import { get, post } from '~/utils/request'

const baseUrl = '/sys/sampleType'

/**
 * 样本类型列表
 */
export function list(params: any) {
  return get(`${baseUrl}/list`, params)
}

/**
 * 新增样本类型
 */
export function add(data: any) {
  return post(`${baseUrl}/add`, data)
}

/**
 * 编辑样本类型(获取)
 */
export function getEdit(id: number) {
  return get(`${baseUrl}/edit`, {
    id,
  })
}

/**
 * 编辑样本类型(提交)
 */
export function postEdit(id: number, data: any) {
  return post(`${baseUrl}/edit`, data, {
    id,
  })
}

/**
 * 删除样本类型
 */
export function remove(ids: number[]) {
  return post(`${baseUrl}/delete`, ids)
}

/**
 * 启用样本类型
 */
export function enable(ids: number[]) {
  return post(`${baseUrl}/enable`, ids)
}

/**
 * 禁用样本类型
 */
export function disable(ids: number[]) {
  return post(`${baseUrl}/disable`, ids)
}
