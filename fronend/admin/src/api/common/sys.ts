import { upload } from '~/utils/request'

const baseUrl = '/sys/common'

/**
 * 文件上传
 */
export function uploadFile(file: File, info?: string, size?: number, ext?: string): Promise<any> {
  return upload(`${baseUrl}/uploadFile`, file, {
    info,
    size,
    ext,
  })
}

/**
 * 图片上传
 */
export function uploadImage(file: File, info?: string, width?: number, height?: number, size?: number): Promise<any> {
  return upload(`${baseUrl}/uploadImage`, file, {
    width,
    height,
    info,
    size,
  })
}
