import { get, post } from '~/utils/request'

const baseUrl = '/sys/smsConfig'

/**
 * 短信配置列表
 */
export function list(params: any): Promise<any> {
  return get<any>(`${baseUrl}/list`, params)
}

/**
 * 编辑短信配置(获取)
 */
export function getEdit(id: number): Promise<any> {
  return get<any>(`${baseUrl}/edit`, {
    id,
  })
}

/**
 * 编辑短信配置(提交)
 */
export function postEdit(id: number, data: any): Promise<any> {
  return post<any>(`${baseUrl}/edit`, data, {
    id,
  })
}

/**
 * 启用
 */
export function enable(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/enable`, ids)
}

/**
 * 禁用
 */
export function disable(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/disable`, ids)
}
