import type { App } from 'vue'
import { Icon } from '@iconify/vue'
import DetailForm from './base/DetailForm.vue'
import DocView from './base/DocView.vue'
import EpFileUpload from './base/EpFileUpload.vue'
import FileUpload from './base/FileUpload.vue'
import GridPanl from './base/GridPanl.vue'
import NameSelect from './base/NameSelect.vue'
import TableList from './base/TableList.vue'
import DeptSelect from './common/DeptSelect.vue'
import DictSelect from './common/DictSelect.vue'
import UserSelect from './common/UserSelect.vue'

export function registerComponents(app: App) {
  app.component('icon', Icon)
  app.component('table-list', TableList)
  app.component('file-upload', FileUpload)
  app.component('ep-file-upload', EpFileUpload)
  app.component('grid-panl', GridPanl)
  app.component('doc-view', DocView)
  app.component('name-select', NameSelect)
  app.component('detail-form', DetailForm)
  app.component('dict-select', DictSelect)
  app.component('dept-select', DeptSelect)
  app.component('user-select', UserSelect)
}
