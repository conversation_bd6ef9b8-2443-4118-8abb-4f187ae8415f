<script setup lang="ts">
import { tips } from '~/utils'

interface Props {
  /** 接受文件类型 */
  accept?: string
  /** 文件大小限制(MB) */
  maxSize?: number // MB
  /** 上传实现 */
  upload: (file: File) => Promise<any>
}
interface Emits {
  (e: 'success', result: any): void
  (e: 'error', error: string): void
}

const props = withDefaults(defineProps<Props>(), {
  accept: '*/*',
  maxSize: 10,
})
const emit = defineEmits<Emits>()

const fileInputRef = useTemplateRef('fileInputRef')
const loading = ref(false)

function triggerUpload() {
  fileInputRef.value?.click()
}

async function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file)
    return

  // 文件大小验证
  if (file.size > props.maxSize * 1024 * 1024) {
    tips.error(`文件大小不能超过 ${props.maxSize}MB`)
    target.value = ''
    return
  }

  loading.value = true

  try {
    const result = await props.upload(file)
    emit('success', result)
  }
  catch (error) {
    emit('error', error as string)
  }
  finally {
    loading.value = false
    target.value = ''
  }
}
</script>

<template>
  <slot :loading="loading" :trigger-upload="triggerUpload" />
  <input
    ref="fileInputRef"
    type="file"
    :accept="accept"
    style="display: none"
    @change="handleFileChange"
  >
</template>
