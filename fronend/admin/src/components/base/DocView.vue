<script setup lang="ts">
import { downloadBlob } from '~/utils/request'

const props = withDefaults(defineProps<{
  blob?: Blob | null
  filename?: string
}>(), {
  blob: null,
  filename: 'preview.pdf',
})

const pdfUrl = ref<string | null>(null)
const iframeRef = ref<HTMLIFrameElement | null>(null)

// 监听传入的blob对象变化，更新pdfUrl
watch(() => props.blob, (newBlob) => {
  if (pdfUrl.value) {
    URL.revokeObjectURL(pdfUrl.value)
    pdfUrl.value = null
  }

  if (newBlob) {
    pdfUrl.value = URL.createObjectURL(newBlob)
  }
}, {
  immediate: true,
})

/**
 * 下载
 */
function handleDownload() {
  if (props.blob) {
    downloadBlob(props.blob, props.filename)
  }
}

/**
 * 打印
 */
function handlePrint() {
  if (iframeRef.value?.contentWindow) {
    iframeRef.value.contentWindow.focus()
    iframeRef.value.contentWindow.print()
  }
}

// 释放
onUnmounted(() => {
  if (pdfUrl.value) {
    URL.revokeObjectURL(pdfUrl.value)
  }
})
</script>

<template>
  <div class="h-full w-full flex flex-col">
    <!-- 工具栏 -->
    <div class="relative right-[36px] top-[-32px] h-0 flex items-center justify-end">
      <el-button-group>
        <el-button :disabled="!pdfUrl" text @click="handleDownload">
          <template #icon>
            <icon icon="ep:download" />
          </template>
          下载
        </el-button>
        <el-button :disabled="!pdfUrl" text @click="handlePrint">
          <template #icon>
            <icon icon="ep:printer" />
          </template>
          打印
        </el-button>
      </el-button-group>
    </div>
    <!-- 内容区域 -->
    <div class="relative flex-grow">
      <iframe
        v-if="pdfUrl"
        ref="iframeRef"
        :src="pdfUrl"
        class="h-full w-full border-none"
      />
    </div>
  </div>
</template>
