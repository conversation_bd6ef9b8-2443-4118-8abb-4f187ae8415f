<script setup lang="ts">
import { useDetailForm } from '~/composables'
import { tips } from '~/utils'

interface DetailFormProps {
  /** 标题 */
  title?: string
  /** 新增标题 */
  addTitle?: string
  /** 编辑标题 */
  editTitle?: string
  /** 对话框宽度 */
  width?: string
  /** 是否可拖拽 */
  draggable?: boolean
  /** 点击遮罩层是否关闭对话框 */
  closeOnClickModal?: boolean
  /** 按下 ESC 是否关闭对话框 */
  closeOnPressEscape?: boolean
  /** 关闭时销毁组件 */
  destroyOnClose?: boolean
  /** 提交按钮文本 */
  submitText?: string
  /** 取消按钮文本 */
  cancelText?: string
  /** 是否回车提交 */
  enterSubmit?: boolean
  /** 表单大小 */
  size?: 'large' | 'default' | 'small'
  /** 初始数据 */
  initData?: Record<string, any>
  /** 提交前回调，返回false则阻止提交 */
  beforeSubmit?: (data: Record<string, any>) => Promise<boolean>
  /** 表单验证规则 */
  rules?: Record<string, any>
  /** 获取API */
  getApi?: (id: number) => Promise<Record<string, any>>
  /** 新增API */
  addApi?: (data: Record<string, any>) => Promise<any>
  /** 编辑API */
  editApi?: (id: number, data: Record<string, any>) => Promise<any>
}

const props = withDefaults(defineProps<DetailFormProps>(), {
  width: '620px',
  draggable: true,
  closeOnClickModal: false,
  closeOnPressEscape: false,
  destroyOnClose: false,
  size: 'default',
  enterSubmit: true,
  submitText: '确定',
  cancelText: '取消',
})
const emits = defineEmits<{
  success: []
  error: [err: Error]
}>()
const attrs = useAttrs()
const { visible, formRef, formSubmiting, isEdit, formData, contentLoading, open, close, submit } = useDetailForm({
  initData: props.initData,
  getApi: props.getApi,
  addApi: props.addApi,
  editApi: props.editApi,
  onBeforeSubmit: props.beforeSubmit,
  onSuccess: () => {
    tips.success()
    emits('success')
  },
  onError: (err) => {
    emits('error', err)
  },
})

const dialogTitle = computed(() => {
  if (isEdit.value && props.editTitle)
    return props.editTitle
  if (!isEdit.value && props.addTitle)
    return props.addTitle
  return `${isEdit.value ? '编辑' : '新增'}${props.title || ''}`
})

function add(initFormData?: any) {
  open(0, initFormData)
}

function edit(id: number, initFormData?: any) {
  open(id, initFormData)
}

defineExpose({
  add,
  edit,
  close,
  formData,
  isEdit,
})
</script>

<template>
  <el-dialog
    v-model="visible" :title="dialogTitle" :width="props.width" :draggable="props.draggable" :destroy-on-close="props.destroyOnClose"
    :close-on-click-modal="props.closeOnClickModal" :close-on-press-escape="props.closeOnPressEscape"
  >
    <el-config-provider :size="props.size">
      <el-form
        ref="formRef" v-loading="contentLoading" :model="formData" :rules="props.rules" label-width="auto"
        v-bind="attrs" @submit.prevent="submit"
      >
        <slot />
        <input v-if="props.enterSubmit" type="submit" class="hidden">
      </el-form>
    </el-config-provider>
    <template #footer>
      <div class="flex justify-end space-x-3">
        <el-button :disabled="formSubmiting" @click="visible = false">
          {{ cancelText }}
        </el-button>
        <el-button type="primary" :loading="formSubmiting" :disabled="contentLoading" @click="submit">
          <template #icon>
            <icon icon="ep:check" />
          </template>
          {{ submitText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
