<script setup lang="ts">
import type { UploadFile, UploadFiles, UploadProps, UploadRawFile, UploadUserFile } from 'element-plus'
import { upload } from '~/api/sys/upfile'
import { tips } from '~/utils'

interface FileUploadProps extends Partial<UploadProps> {
  // 上传模式
  mode?: 'image' | 'file'
  // 文件大小限制 (MB)
  size?: number
  // 是显示移动按钮
  showMove?: boolean
}

const props = withDefaults(defineProps<FileUploadProps>(), {
  mode: 'file',
  size: 50,
  drag: true,
  showFileList: true,
})

const emits = defineEmits<{
  // 'update:file-list': [UploadUserFile[]]
  submitCompleted: [UploadUserFile[]]
  uploadFail: []
}>()

// 响应式数据
const fileListModel = defineModel<UploadUserFile[]>('file-list')
const elUploadRef = useTemplateRef('elUploadRef')
const fileList = ref<UploadUserFile[]>(props.fileList || [])
const previewDialogVisible = ref(false)
const previewUrl = ref('')
const previewMode = ref<'image' | 'pdf'>('image')
const isPasteEvent = ref(false)

// 计算属性
const listType = computed<UploadProps['listType']>(() => {
  return 'picture-card'
})

const multiple = computed(() => {
  return props.limit !== 1
})

const accept = computed(() => {
  if (props.accept)
    return props.accept
  return props.mode === 'image' ? 'image/*' : '*'
})

const autoUpload = computed(() => {
  return props.autoUpload
})

const showFileList = computed(() => {
  return props.showFileList
})

const showUploadButton = computed(() => {
  return !props.limit || fileList.value.length < props.limit
})

const successFileList = computed(() => {
  return fileList.value.filter(file => file.status === 'success')
})

const isMobile = computed(() => {
  if (typeof window === 'undefined')
    return false
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
})

const uploadTip = computed(() => {
  const sizeText = props.size ? `单个文件不超过${props.size}MB` : ''
  const limitText = props.limit ? `最多上传${props.limit}个文件` : ''
  return [sizeText, limitText].filter(Boolean).join('，')
})

// 监听变更
watch(() => successFileList.value, (newVal, oldVal) => {
  if (getFileListHash(newVal) !== getFileListHash(oldVal)) {
    fileListModel.value = newVal
    // emits('update:file-list', newVal)
  }
})

// 监听可见性，注册粘贴事件
useIntersectionObserver(
  elUploadRef,
  ([{ isIntersecting }]) => {
    if (isIntersecting)
      addPasteEvent()
    else
      removePasteEvent()
  },
)

// 处理剪贴板粘贴
function handlePaste(event: ClipboardEvent) {
  // 检查是否为图片模式或允许所有文件类型
  if (props.mode === 'file' || props.mode === 'image') {
    const clipboardData = event.clipboardData
    if (!clipboardData)
      return

    const items = Array.from(clipboardData.items)

    for (const item of items) {
      if (item.type.startsWith('image/')) {
        event.preventDefault()

        // 检查文件数量限制
        if (props.limit && fileList.value.length >= props.limit) {
          tips.error(`最多只能上传${props.limit}个文件`)
          return
        }

        const file = item.getAsFile()
        if (!file)
          continue

        // 生成文件名
        const timestamp = Date.now()
        const extension = file.type.split('/')[1] || 'png'
        const fileName = `clipboard-image-${timestamp}.${extension}`

        // 创建新的File对象
        const newFile = new File([file], fileName, { type: file.type }) as UploadRawFile
        newFile.uid = Date.now() + Math.random()

        // 检查文件大小
        if (!beforeUpload(newFile as UploadRawFile)) {
          return
        }

        // 上传对象
        const upfile: UploadUserFile = {
          uid: newFile.uid,
          name: fileName,
          size: newFile.size,
          status: 'ready',
          raw: newFile,
          url: URL.createObjectURL(newFile),
        }

        // 添加到文件列表
        fileList.value.push(upfile)

        // 如果是自动上传模式，立即上传
        if (autoUpload.value) {
          uploadFile({ file: newFile })
        }

        tips.success('剪贴板图片已添加到上传列表')
        break // 只处理第一个
      }
    }
  }
}

function handleKeydown(event: KeyboardEvent) {
  if ((event.ctrlKey || event.metaKey) && event.key === 'v') {
    const pasteEvent = new ClipboardEvent('paste', {
      clipboardData: (event as any).clipboardData || (window as any).clipboardData,
    })
    const items = pasteEvent.clipboardData?.items
    if (items && items.length > 0) {
      handlePaste(pasteEvent)
    }
  }
}

// 简单根据关键属性生成文件列表哈希值
function getFileListHash(files: UploadUserFile[]): string {
  return files.map(file => `${file.uid}-${file.status}-${file.url || ''}`).join('|')
}

// 判断是否为图片文件
function isImage(file: UploadUserFile | UploadFile) {
  if (file.raw?.type) {
    return file.raw.type.startsWith('image/')
  }
  if (file.url) {
    const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
    const ext = file.url!.toLowerCase()
    return imageExts.some(t => ext.endsWith(t))
  }
  return false
}

// 判断是否为PDF文件
function isPdf(file: UploadUserFile | UploadFile) {
  if (file.raw?.type)
    return file.raw.type === 'application/pdf'
  if (file.url)
    return file.url.toLowerCase().endsWith('.pdf')
  if (file.name)
    return file.name.toLowerCase().endsWith('.pdf')
  return false
}

// 预览
function previewFile(file: UploadUserFile | UploadFile) {
  if (isImage(file)) {
    previewMode.value = 'image'
    previewUrl.value = file.url!
    previewDialogVisible.value = true
  }
  else if (isPdf(file) && !isMobile.value) {
    previewMode.value = 'pdf'
    previewUrl.value = file.url!
    previewDialogVisible.value = true
  }
  else {
    downloadFile(file)
  }
}

// 下载
function downloadFile(file: UploadUserFile | UploadFile) {
  const a = document.createElement('a')
  a.href = file.url!
  a.download = file.name
  a.style.display = 'none'
  a.target = '_blank'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}

// 上传前检查
function beforeUpload(file: UploadRawFile) {
  // 检查文件大小
  if (props.size && file.size > props.size * 1024 * 1024) {
    tips.error(`文件大小不能超过${props.size}MB`)
    return false
  }

  // 检查文件类型
  if (props.mode === 'image' && !file.type.startsWith('image/')) {
    tips.error('只能上传图片文件')
    return false
  }

  if (props.beforeUpload) {
    return props.beforeUpload(file)
  }

  return true
}

// 更新文件状态
function updateFileStatus(uid: number, status: 'ready' | 'uploading' | 'success' | 'fail', url?: string) {
  fileList.value = fileList.value.map(file =>
    file.uid === uid ? { ...file, status, url: url || file.url } : file,
  )
}

// 上传
async function uploadFile(options: any) {
  const { file } = options
  updateFileStatus(file.uid, 'uploading')
  try {
    const result = await upload(file)
    updateFileStatus(file.uid, 'success', result.data[0].fullPath)

    if (fileList.value.length > 0 && fileList.value.every(f => f.status === 'success'))
      emits('submitCompleted', fileList.value)
  }
  catch {
    // 在未抛出异常时，ep内部会自动把status设为success，所以这里采用延迟方案置状态为失败
    setTimeout(() => {
      updateFileStatus(file.uid, 'fail')
      if (fileList.value.length > 0 && fileList.value.every(f => f.status === 'fail'))
        emits('uploadFail')
    })
  }
}

// 列表变化勾子
function onChange(file: UploadFile, files: UploadFiles) {
  fileList.value = [...files]
  props.onChange?.(file, files)
}

// 移除文件勾子
function onRemove(file: UploadFile, files: UploadFiles) {
  revokeObjectURL(file as UploadUserFile)
  fileList.value = [...files]
  props.onRemove?.(file, files)
}

// 移除文件处理
function handleRemove(file: UploadFile) {
  elUploadRef.value?.handleRemove(file)
}

// 移动文件位置
function handleMove(file: UploadFile, direction: 'left' | 'right') {
  const currentIndex = fileList.value.findIndex(f => f.uid === file.uid)
  if (currentIndex === -1)
    return

  const fileCount = fileList.value.length
  let newIndex: number

  if (direction === 'left')
    newIndex = currentIndex === 0 ? fileCount - 1 : currentIndex - 1
  else
    newIndex = currentIndex === fileCount - 1 ? 0 : currentIndex + 1

  const newList = [...fileList.value]
    ;[newList[currentIndex], newList[newIndex]] = [newList[newIndex], newList[currentIndex]]

  fileList.value = newList
}

// 清理临时URL，防止内存泄漏
function revokeObjectURL(file: UploadUserFile) {
  if (file.url && file.url.startsWith('blob:')) {
    URL.revokeObjectURL(file.url)
  }
}

// 提交上传
function submit() {
  if (autoUpload.value) {
    return false
  }

  if (fileList.value.length === 0) {
    tips.error('请选择要上传的文件')
    return false
  }

  // 提前批量检查
  for (const file of fileList.value.filter(f => f.status === 'ready')) {
    if (!beforeUpload(file.raw as UploadRawFile))
      return false
  }

  elUploadRef.value?.submit()
  return true
}

// 清空文件
function clearFiles() {
  fileList.value = []
}

// 启用粘贴事件监听
function addPasteEvent() {
  if (!isPasteEvent.value) {
    isPasteEvent.value = true
    document.addEventListener('paste', handlePaste)
    document.addEventListener('keydown', handleKeydown)
  }
}

// 移除粘贴事件监听
function removePasteEvent() {
  if (isPasteEvent.value) {
    isPasteEvent.value = false
    document.removeEventListener('paste', handlePaste)
    document.removeEventListener('keydown', handleKeydown)
  }
}

// 组件卸载时清理事件及临时URL
onUnmounted(() => {
  removePasteEvent()
  fileList.value.forEach(file => revokeObjectURL(file))
})

// 暴露方法
defineExpose({
  elUploadRef,
  submit,
  clearFiles,
})
</script>

<template>
  <el-upload
    ref="elUploadRef" v-bind="props" v-model:file-list="fileList" :show-file-list="showFileList"
    :auto-upload="autoUpload" :list-type="listType" :accept="accept" :multiple="multiple" :http-request="uploadFile"
    :before-upload="beforeUpload" class="upload" :class="{ 'hide-add': !showUploadButton }" :on-change="onChange"
    :on-remove="onRemove"
  >
    <!-- 上传按钮 -->
    <div v-if="showUploadButton" class="h-full flex flex-col select-none items-center gap-3" :title="uploadTip">
      <icon icon="ep:plus" size="5" />
      <el-text v-if="props.drag" type="info" size="small" class="block text-center">
        将文件拖到此处<br>或点击上传
      </el-text>
    </div>
    <template #file="{ file }">
      <div v-loading="file.status === 'uploading'" class="w-full">
        <img v-if="isImage(file)" class="el-upload-list__item-thumbnail" :src="file.url" alt="">
        <div v-else class="grid h-full place-items-center">
          <icon v-if="isPdf(file)" icon="iwwa:file-pdf" size="18" class="opacity-60" />
          <icon v-else icon="ep:document" size="18" class="opacity-60" />
        </div>
        <div class="relative top-[-22px] truncate text-center text-xs text-gray-500">
          {{ file.name }}
        </div>
        <span class="el-upload-list__item-actions">
          <span v-if="props.showMove" title="左移" @click="handleMove(file, 'left')">
            <icon icon="ep:arrow-left" size="5" />
          </span>
          <span v-if="isImage(file) || (isPdf(file) && !isMobile)" title="预览" @click="previewFile(file)">
            <icon icon="ep:zoom-in" size="5" />
          </span>
          <span v-else title="下载" @click="previewFile(file)">
            <icon icon="ep:download" size="5" />
          </span>
          <span title="删除" @click="handleRemove(file)">
            <icon icon="ep:delete" size="5" />
          </span>
          <span v-if="props.showMove" title="右移" @click="handleMove(file, 'right')">
            <icon icon="ep:arrow-right" size="5" />
          </span>
        </span>
      </div>
    </template>
  </el-upload>
  <el-dialog
    v-model="previewDialogVisible" draggable destroy-on-close :width="previewMode === 'pdf' ? '90%' : ''"
    :title="previewMode !== 'image' ? '文件预览' : ''"
  >
    <iframe v-if="previewMode === 'pdf'" :src="previewUrl" frameborder="0" class="h-[90vh] w-full border-none" />
    <div v-else class="grid min-h-50 place-items-center">
      <img :src="previewUrl" alt="" class="max-w-full">
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
.upload {
  :deep(.el-upload-dragger) {
    height: 100% !important;
  }
}

.hide-add {
  :deep(.el-upload--picture-card) {
    display: none;
  }
}
</style>
