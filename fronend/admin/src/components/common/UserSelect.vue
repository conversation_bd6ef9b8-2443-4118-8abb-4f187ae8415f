<script setup lang="ts">
import { getCommonUsers } from '~/api/sys/user'

interface UserSelectProps {
  /** 数据，如果传递了数据则以传递的数据为准 */
  options?: any[]
  /** 显示指定科室用户 */
  dept?: number
  /** 当指定了科室用户时，指示是否包含子级科室用户 */
  children?: boolean
}

const props = withDefaults(defineProps<UserSelectProps>(), {
})
const attrs = useAttrs()

const modelValue = defineModel<number>()
const modelName = defineModel<string>('name')
const options = ref<any[]>([])
const filterText = ref('')

// 过滤后的数据
const data = computed(() => {
  return options.value.filter((item) => {
    return item.loginName?.includes(filterText.value)
      || item.userName?.includes(filterText.value)
      || item.py?.includes(filterText.value)
      || item.phone?.includes(filterText.value)
      || item.email?.includes(filterText.value)
  })
})

watch(() => props.options, (val) => {
  if (val) {
    options.value = val
  }
})

// 处理name
function handleChange(value: any) {
  const node = options.value?.find(o => o.id === value)
  modelName.value = node?.userName || '';
  (attrs.onChange as ((value: any) => void) | undefined)?.(value)
}

// 过滤
function handleFilter(query: string) {
  filterText.value = query
}

onMounted(async () => {
  if (props.options) {
    options.value = props.options
  }
  else {
    const res = await getCommonUsers({
      dept: props.dept,
      children: props.children,
    })
    options.value = res.data
  }
})
</script>

<template>
  <el-select v-model="modelValue" :filter-method="handleFilter" filterable placeholder="请选择" clearable v-bind="attrs" @change="handleChange">
    <el-option v-for="item in data" :key="item.id" :label="item.userName || item.loginName" :value="item.id" />
  </el-select>
</template>
