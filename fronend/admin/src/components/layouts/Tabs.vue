<script lang="ts" setup>
import { useTabs } from '~/composables'

const {
  tabs,
  addTab,
  activeTab,
  removeTab,
  closeOtherTabs,
  closeRightTabs,
  closeLeftTabs,
  closeAllTabs,
  resetTabs,
} = useTabs()

const route = useRoute()
const router = useRouter()

// 标签点击事件
function handleTabClick(tab: any) {
  if (tab.paneName !== route.name) {
    router.push(tab.paneName)
  }
}

// 处理标签操作命令
function handleTabCommand(command: string, name: string) {
  switch (command) {
    case 'current':
      removeTab(name)
      break
    case 'other':
      closeOtherTabs(name)
      break
    case 'right':
      closeRightTabs(name)
      break
    case 'left':
      closeLeftTabs(name)
      break
    case 'all':
      closeAllTabs()
      break
  }
}

// 监听路由变化自动添加标签
watch(
  () => [route.name],
  (to) => {
    to && addTab(route)
  },
  { immediate: true },
)

onUnmounted(() => {
  resetTabs()
})
</script>

<template>
  <div class="relative">
    <el-tabs
      v-model="activeTab"
      class="tabs select-none pl-0 sm:pl-1"
      type="card"
      @tab-remove="removeTab"
      @tab-click="handleTabClick"
    >
      <el-tab-pane v-for="tab in tabs" :key="tab.name" :label="tab.title" :name="tab.name" :closable="tab.name !== '/'">
        <template #label>
          <el-dropdown trigger="contextmenu" @command="handleTabCommand($event, tab.name)">
            <div class="px-1 py-2">
              {{ tab.title }}
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="current" class="!px-10">
                  关闭
                </el-dropdown-item>
                <el-dropdown-item command="other" class="!px-10" divided>
                  关闭其他
                </el-dropdown-item>
                <el-dropdown-item command="all" class="!px-10">
                  关闭所有
                </el-dropdown-item>
                <el-dropdown-item command="right" class="!px-10" divided>
                  关闭右侧
                </el-dropdown-item>
                <el-dropdown-item command="left" class="!px-10">
                  关闭左侧
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-tab-pane>
    </el-tabs>
    <div class="absolute top-[39px] w-[4px] border-b border-gray-200 border-b-solid dark:border-gray-700" />
  </div>
</template>

<style scoped lang="scss">
.tabs :deep(.el-tabs__header) {
  margin-bottom: 0 !important;
}
</style>
