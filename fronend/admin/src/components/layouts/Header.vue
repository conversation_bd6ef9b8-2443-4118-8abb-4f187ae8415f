<script lang="ts" setup>
import type { NoticeMessage } from '~/utils'
import type { MenuItem } from '~/utils/router'
import ChangePassword from '~/components/auth/ChangePassword.vue'
import { isDark, toggleDark, useAuth, useMenu } from '~/composables'
import { getConfig, noticeClient } from '~/utils'

const router = useRouter()
const { getUser, getDept, setDept, logout } = useAuth()
const { toggle } = useFullscreen()
const { activeModule, allMenu, setActiveModule } = useMenu(true)

// 计算属性
const config = computed(() => getConfig())
const currentUser = computed(() => getUser())
const currentDept = computed(() => {
  const deptId = Number(getDept() || 0)
  return currentUser.value?.deptPosts?.find(item => item.deptId === deptId)
})
const changePasswordVisible = ref(false)

// 切换科室
function handleChangeDept(deptId: number) {
  if (getDept() === deptId) {
    return
  }
  setDept(deptId)
  window.location.reload()
}

// 登出
async function handleLogout(): Promise<void> {
  await logout()
  router.push(config.value.loginPath)
}

// 处理通知消息
function handleNoticeMessage(message: NoticeMessage) {
  // 暂定用99代表紧急通知
  if (message) {
    ElNotification({
      title: message.title,
      message: message.content,
      duration: 0,
      position: 'bottom-right',
      onClose: () => {
        noticeClient.markRead([message.id])
      },
    })
  }
}

// 注册相关监听与事件
useEventListener(window, 'beforeunload', (e: any) => {
  if (config.value.beforeunloadWarning) {
    e.returnValue = '系统不会保留正在编辑的内容，确定要刷新吗？'
  }
})

let noticeMessageUnsubscribe: () => void
onMounted(() => {
  changePasswordVisible.value = currentUser.value?.needChangePassword === true

  noticeMessageUnsubscribe = noticeClient.onMessage(handleNoticeMessage)
  noticeClient.connect()
})

onBeforeUnmount(() => {
  noticeMessageUnsubscribe?.()
  noticeClient.disconnect()
})
</script>

<template>
  <el-menu mode="horizontal" :ellipsis="false" class="menu">
    <!-- 搜索按钮 -->
    <!-- <el-button text class="h-full !ml-0" title="搜索">
      <icon icon="ep:search" width="24" height="24" />
    </el-button> -->

    <!-- Logo和标题 -->
    <div class="ml-2 flex select-none items-center justify-center gap-2">
      <!-- <img src="/logo.png" class="h-8 w-8"> -->
      <span class="ml-3 hidden text-lg sm:inline">{{ config.appName }}</span>
    </div>

    <div class="flex-grow select-none" />

    <!-- 主菜单 -->
    <el-menu mode="horizontal" class="menu-main mr-8 flex items-center gap-2" :default-active="activeModule?.name" :ellipsis="false">
      <el-menu-item v-for="item in allMenu" :key="item.name" :index="item.name" class="rounded" @click="setActiveModule(item as MenuItem)">
        <div class="flex items-center px-1">
          <icon :icon="item.icon" :width="item.size || 24" :height="item.size || 24" class="opacity-80" />
          <span class="ml-2 text-sm">
            {{ item.title }}
          </span>
        </div>
      </el-menu-item>
    </el-menu>

    <!-- 通知按钮 -->
    <el-button text class="h-full" @click="router.push('/common/notice')">
      <el-badge :value="noticeClient.unreadCount.value || ''" class="item">
        <icon icon="tdesign:notification" width="18" height="18" class="opacity-70" />
      </el-badge>
    </el-button>

    <!-- 主题切换 -->
    <el-button text class="h-full !ml-0" @click="toggleDark()">
      <icon v-if="isDark" icon="line-md:moon-filled-to-sunny-filled-transition" width="20" height="20" />
      <icon v-else icon="line-md:moon-rising-twotone-loop" width="20" height="20" />
    </el-button>

    <!-- 全屏 -->
    <el-button text class="h-full !ml-0" @click="toggle">
      <icon icon="qlementine-icons:fullscreen-16" width="18" height="18" />
    </el-button>

    <!-- 用户菜单 -->
    <el-sub-menu index="9">
      <template #title>
        <a class="flex items-center justify-center">
          <icon icon="line-md:account" width="20" height="20" class="opacity-70" />
          <span class="ml-2 hidden sm:inline">{{ currentUser?.userName }}</span>
        </a>
      </template>
      <el-sub-menu v-if="currentUser?.deptPosts?.length" index="9-1" class="menu-dept pt-2 !h-11">
        <template #title>
          <div class="w-full flex items-center justify-between">
            <span class="!color-initial">{{ currentDept?.deptName }}</span>
            <span class="text-gray-400 dark:text-gray-500">{{ currentDept?.postName }}</span>
          </div>
        </template>
        <el-menu-item
          v-for="item in currentUser.deptPosts" :key="item.dept_id" :index="`9-1-${item.deptId}`"
          class="w-full justify-between !h-11 !color-initial" @click="handleChangeDept(item.deptId)"
        >
          <span>{{ item.deptName }}</span>
          <span class="text-gray-400 dark:text-gray-500">{{ item.postName }}</span>
        </el-menu-item>
      </el-sub-menu>
      <div v-if="currentUser?.deptPosts?.length" class="border-t-1 border-gray-200 border-t-solid !h-2 dark:border-gray-700" />
      <el-menu-item
        index="9-2" class="justify-center justify-between !h-11 !color-initial"
        @click="router.push('/common/profile')"
      >
        <span>个人中心</span>
        <icon icon="ep:setting" width="18" height="18" class="text-gray-400 dark:text-gray-500" />
      </el-menu-item>
      <el-menu-item index="9-3" class="justify-center justify-between !h-11" @click="handleLogout">
        <span>安全退出</span>
        <icon icon="ep:lock" width="18" height="18" class="text-gray-400 dark:text-gray-500" />
      </el-menu-item>
    </el-sub-menu>
  </el-menu>

  <!-- 需要修改密码 -->
  <el-dialog v-model="changePasswordVisible" title="请修改您的登录密码" width="460px" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false">
    <ChangePassword submit-center />
  </el-dialog>
</template>

<style scoped lang="scss">
.menu {
  :deep(.is-active .el-sub-menu__title) {
    border-bottom: initial !important;
    color: initial !important;
  }
}

.menu-dept {
  :deep(.el-sub-menu__title .el-sub-menu__icon-arrow) {
    color: #9ca3af;
  }
}
:deep(.menu-main .el-menu-item) {
  border-bottom: none !important;
  &.is-active {
    background: var(--el-color-primary) !important;
    color: #fff !important;
  }
}
</style>
