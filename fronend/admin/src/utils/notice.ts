import { getConfig } from './config'
import { get, post } from './request'

export interface NoticeMessage {
  id: number
  title: string
  content: string
  type: number
  isRead: boolean
  createTime: string
  link?: string
  senderId?: number
  senderName?: string
  data?: Record<string, any>
}

export class NoticeClient {
  private baseUrl: string
  private eventSource: ReturnType<typeof useEventSource> | null = null

  // 响应式状态
  public readonly connected = ref(false)
  public readonly unreadCount = ref(0)
  public readonly messages = ref<NoticeMessage[]>([])

  // 事件回调管理
  private messageHandlers = new Set<(message: NoticeMessage) => void>()
  private connectionHandlers = new Set<(connected: boolean) => void>()

  constructor(baseUrl = '/sse/notice') {
    this.baseUrl = baseUrl
    window.addEventListener('tokenRefreshed', this.connect)
  }

  /**
   * 创建SSE连接
   */
  public connect = async (): Promise<void> => {
    if (this.eventSource) {
      this.disconnect()
    }

    // 先获取连接token
    const token = await this.getConnectionToken()
    const url = `${getConfig().apiUrl}${this.baseUrl}/connect?t=${token}`

    return new Promise((resolve, reject) => {
      try {
        this.eventSource = useEventSource(url, ['notice'])

        // 监听状态变化
        watch(this.eventSource.status, (status) => {
          const isConnected = status === 'OPEN'
          this.setConnected(isConnected)

          if (isConnected) {
            this.getUnreadCount()
            resolve()
          }
          else if (status === 'CLOSED') {
            this.setConnected(false)
          }
        }, { immediate: true })

        // 监听消息事件
        watch(this.eventSource.data, (data) => {
          this.handleMessage(data as string)
        })

        // 监听错误
        watch(this.eventSource.error, (error) => {
          if (error) {
            this.setConnected(false)
          }
        })
      }
      catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 断开连接
   */
  public disconnect = () => {
    if (this.eventSource) {
      this.setConnected(false)
      this.eventSource.close()
      this.eventSource = null
    }
  }

  /**
   * 处理通知消息
   */
  private handleMessage = (data: string) => {
    const message: NoticeMessage = JSON.parse(data)

    // 更新本地消息列表
    this.messages.value.unshift(message)
    // 更新未读数量
    if (!message.isRead) {
      this.unreadCount.value++
    }

    // 触发消息回调
    this.messageHandlers.forEach(handler => handler(message))
  }

  /**
   * 设置连接状态
   */
  private setConnected = (connected: boolean) => {
    this.connected.value = connected
    this.connectionHandlers.forEach(handler => handler(connected))
  }

  /**
   * 获取SSE连接token
   */
  private getConnectionToken = async (): Promise<string> => {
    const { data } = await get(`${this.baseUrl}/getToken`)
    return data
  }

  /**
   * 获取未读消息数量
   */
  public getUnreadCount = async (): Promise<number> => {
    const { data } = await get(`${this.baseUrl}/unreadCount`)
    this.unreadCount.value = data
    return data
  }

  /**
   * 标记消息为已读
   */
  public markRead = async (ids: number[]) => {
    await post(`${this.baseUrl}/markRead`, ids)

    // 更新本地状态
    this.messages.value.forEach((msg) => {
      if (ids.includes(msg.id)) {
        msg.isRead = true
      }
    })

    await this.getUnreadCount()
  }

  /**
   * 标记所有消息为已读
   */
  public markAllRead = async () => {
    await post(`${this.baseUrl}/markAllRead`)

    // 更新本地状态
    this.messages.value.forEach(msg => msg.isRead = true)
    this.unreadCount.value = 0
  }

  /**
   * 删除消息
   */
  public delete = async (ids: number[]) => {
    await post(`${this.baseUrl}/delete`, ids)
    // 更新本地状态 - 移除已删除的消息
    this.messages.value = this.messages.value.filter(msg => !ids.includes(msg.id))
    // 重新获取未读数量
    await this.getUnreadCount()
  }

  /**
   * 删除所有消息
   */
  public deleteAll = async () => {
    await post(`${this.baseUrl}/deleteAll`)
    // 清空本地消息列表
    this.messages.value = []
    this.unreadCount.value = 0
  }

  /**
   * 获取最新n条消息
   */
  public getTop = (top: number): Promise<any> => {
    return get(`${this.baseUrl}/top`, { top })
  }

  /**
   * 分页查询消息
   */
  public getPage = (params: any): Promise<any> => {
    return get(`${this.baseUrl}/page`, params)
  }

  /**
   * 获取在线用户数量
   */
  public getOnlineCount = async (): Promise<number> => {
    const { data } = await get(`${this.baseUrl}/onlineCount`)
    return data
  }

  /**
   * 判断用户是否在线
   */
  public isUserOnline = async (userId: number): Promise<boolean> => {
    const { data } = await get(`${this.baseUrl}/isOnline`, { userId })
    return data
  }

  /**
   * 添加消息监听器
   */
  public onMessage = (handler: (message: NoticeMessage) => void) => {
    this.messageHandlers.add(handler)
    // 返回一个用于删除该消息处理函数
    return () => this.messageHandlers.delete(handler)
  }

  /**
   * 添加连接状态监听器
   */
  public onConnectionChange = (handler: (connected: boolean) => void) => {
    this.connectionHandlers.add(handler)
    return () => this.connectionHandlers.delete(handler)
  }

  /**
   * 销毁实例
   */
  public destroy = () => {
    this.disconnect()
    this.messageHandlers.clear()
    this.connectionHandlers.clear()
    window.removeEventListener('tokenRefreshed', this.connect)
  }
}

// 导出单例实例
export const noticeClient = new NoticeClient()
