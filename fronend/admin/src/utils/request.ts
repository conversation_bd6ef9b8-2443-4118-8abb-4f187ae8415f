import { useFetch, useTimeoutFn } from '@vueuse/core'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuth } from '~/composables'
import { getConfig } from './config'
import { getRouter } from './router'

const { getAccessToken, refreshToken, clearToken, getDept } = useAuth()

// 刷新token事件
const tokenRefreshedEvent = new Event('tokenRefreshed')

// 响应结果
export interface Result<T = any> {
  code: number
  msg: string
  data: T
}

// 将js的驼峰命名转换为后端下划线命名
function camelToSnakeCase(str: string) {
  // 暂时保持统一不转命名风格
  return str
  // return str ? str.replace(/([A-Z])/g, '_$1').toLowerCase() : str
}

// 确保单一请求
export class SingleRequest<T = any> {
  private pendingPromise?: Promise<T>

  async exec(request: () => Promise<T>): Promise<T> {
    if (this.pendingPromise)
      return this.pendingPromise

    try {
      this.pendingPromise = request()
      const result = await this.pendingPromise
      return result
    }
    finally {
      this.pendingPromise = undefined
    }
  }
}

// 下载结果
interface DownloadResult {
  filename: string
  size: number
  blob: Blob
}

type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE'

// 请求选项
interface RequestOptions {
  method?: HttpMethod
  params?: Record<string, any>
  data?: any
  headers?: Record<string, string>
  timeout?: number
  signal?: AbortSignal
  disableAutoHandleError?: boolean
}

// 构建url
function buildUrl(url: string, params?: Record<string, any>) {
  if (url.startsWith('http'))
    return url

  const baseUrl = `${getConfig().apiUrl}${url}`
  if (!params)
    return baseUrl

  const query = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (value != null) {
      if (Array.isArray(value)) {
        value.forEach(v => query.append(key, String(v)))
      }
      else if (typeof value === 'object') {
        query.append(camelToSnakeCase(key), JSON.stringify(value))
      }
      else {
        query.append(camelToSnakeCase(key), String(value))
      }
    }
  })
  return `${baseUrl}?${query.toString()}`
}

// 常规错误处理
function handleError(error: string) {
  const message = error === 'Failed to fetch' ? '与服务器连接失败' : error || '处理请求失败'
  ElMessage({ message, type: 'error', grouping: true })
  return Promise.reject(message)
}

let loginConfirm = false
// 认证错误处理
async function handleAuthError() {
  clearToken()
  setTimeout(() => {
    const router = getRouter()
    const loginPath = getConfig().loginPath

    if (!loginConfirm && router.currentRoute.value.path !== loginPath) {
      loginConfirm = true
      ElMessageBox.confirm('登录已过期，请重新登录!', '提示', {
        type: 'error',
        closeOnClickModal: false,
        closeOnPressEscape: false,
      }).then(() => {
        router.push({
          path: loginPath,
          query: { redirect: router.currentRoute.value.fullPath },
        })
      }).finally(() => {
        loginConfirm = false
      })
    }
  }, 500)

  return Promise.reject(new Error('用户未登录或登录已过期'))
}

// Token 刷新相关
const refreshPromise = new SingleRequest<boolean>()
let lastRefreshTime = 0
let lastRefreshResult = false

// Token 刷新处理
async function handleTokenRefresh() {
  const now = Date.now() / 1000
  // 10秒内刷新过
  if (lastRefreshTime + 10 > now)
    return lastRefreshResult

  return refreshPromise.exec(async () => {
    lastRefreshResult = await refreshToken()
    lastRefreshTime = now
    if (lastRefreshResult) {
      window.dispatchEvent(tokenRefreshedEvent)
    }
    return lastRefreshResult
  })
}

// 获取认证头信息
function getAuthHeader(): Record<string, string> {
  const token = getAccessToken()
  const dept = getDept()
  return token
    ? { Authorization: `Bearer ${token}`, ...(dept ? { 'X-Dept': dept.toString() } : {}) }
    : {}
}

// 核心请求函数
export async function request<T = any>(url: string, options: RequestOptions = {}) {
  const { method = 'GET', params, data, headers = {}, timeout, signal } = options
  const isFormData = data instanceof FormData

  const requestHeaders: Record<string, string> = {
    ...headers,
    ...getAuthHeader(),
  }

  if (!isFormData && !requestHeaders['Content-Type'])
    requestHeaders['Content-Type'] = 'application/json'

  // 构建请求配置
  const fetchOptions: RequestInit = {
    method,
    headers: requestHeaders,
    body: data ? (isFormData ? data : JSON.stringify(data)) : undefined,
    signal,
  }

  // 处理超时
  if (timeout && !signal) {
    const controller = new AbortController()
    const { start } = useTimeoutFn(() => {
      controller.abort()
      handleError('网络请求超时')
    }, timeout)
    start()
    fetchOptions.signal = controller.signal
  }

  try {
    const fetch = useFetch<Result<T>>(buildUrl(url, params), fetchOptions)
    const res = await fetch.json()

    if (!res.statusCode.value) {
      return options.disableAutoHandleError ? Promise.reject(res.error.value) : handleError(res.error.value)
    }

    if (res.statusCode.value === 401) {
      if (await handleTokenRefresh())
        return request<T>(url, options)
      return handleAuthError()
    }

    const result: Result<T> = res.data.value || await fetch.response.value?.json()
    if (res.statusCode.value === 200 && [0, 200].includes(result.code))
      return result

    return options.disableAutoHandleError ? Promise.reject(result.msg) : handleError(result.msg)
  }
  catch (err: unknown) {
    return handleError(err as string)
  }
}

// HTTP 方法快捷函数
export function get<T = any>(url: string, params?: Record<string, any>, options: Omit<RequestOptions, 'method' | 'params'> = {}) {
  return request<T>(url, { ...options, method: 'GET', params })
}

export function post<T = any>(url: string, data?: any, params?: Record<string, any>, options: Omit<RequestOptions, 'method' | 'data'> = {}) {
  return request<T>(url, { ...options, method: 'POST', data, params })
}

export function put<T = any>(url: string, data?: any, params?: Record<string, any>, options: Omit<RequestOptions, 'method' | 'data'> = {}) {
  return request<T>(url, { ...options, method: 'PUT', data, params })
}

export function del<T = any>(url: string, data?: any, params?: Record<string, any>, options: Omit<RequestOptions, 'method'> = {}) {
  return request<T>(url, { ...options, method: 'DELETE', data, params })
}

// 文件上传
export async function upload<T = any>(
  url: string,
  file: File | File[],
  data?: Record<string, any>,
  options: Omit<RequestOptions, 'method' | 'data'> = {},
) {
  const formData = new FormData()

  if (Array.isArray(file)) {
    file.forEach((item, _) => {
      formData.append(`files`, item)
    })
  }
  else {
    formData.append('files', file)
  }

  if (data) {
    Object.entries(data).forEach(([key, value]) => {
      if (value != null) {
        formData.append(key, typeof value === 'object' ? JSON.stringify(value) : String(value))
      }
    })
  }

  const { headers = {}, ...restOptions } = options
  const uploadHeaders = Object.fromEntries(
    Object.entries(headers).filter(([key]) => key.toLowerCase() !== 'content-type'),
  )

  return request<T>(url, {
    ...restOptions,
    method: 'POST',
    headers: uploadHeaders,
    data: formData,
  })
}

// 解析下载文件名
function parseContentDisposition(header: string | null): string | undefined {
  if (!header)
    return undefined

  const match = header.match(/filename\*?=(?:"([^"]+)"|([^;]+))/i)
  if (!match)
    return undefined

  const filename = match[1] || match[2]
  try {
    if (filename.startsWith('utf-8\'\'')) {
      return decodeURIComponent(filename.substring(7))
    }
    return decodeURIComponent(filename)
  }
  catch {
    return filename.trim()
  }
}

// 文件下载
export async function download(
  url: string,
  options: RequestOptions & { filename?: string } = {},
): Promise<DownloadResult> {
  const { filename, params, data: reqData, method = 'POST', ...rest } = options

  try {
    const reqOptions: RequestInit = {
      method,
      ...rest,
      headers: {
        ...rest.headers,
        ...getAuthHeader(),
      },
    }

    if (method !== 'GET' && reqData) {
      reqOptions.body = JSON.stringify(reqData)
      reqOptions.headers = {
        'Content-Type': 'application/json',
        ...reqOptions.headers,
      }
    }

    const fetch = useFetch(
      buildUrl(url, params),
      reqOptions,
    )

    const { data, error, statusCode } = await fetch.blob()
    if (error.value) {
      if (statusCode.value === 401) {
        if (await handleTokenRefresh())
          return download(url, options)
        return handleAuthError()
      }

      try {
        // 可能返回json结果，从中获取错误
        const result = await fetch.response.value?.json()
        if (result && result.msg) {
          return handleError(result.msg)
        }
      }
      catch { }

      return handleError(error.value)
    }

    if (!data.value)
      return handleError('下载数据为空')

    const blob = data.value
    const contentDisposition = fetch.response.value?.headers?.get('content-disposition') || null
    const finalFilename = filename || parseContentDisposition(contentDisposition) || 'download'

    return {
      filename: finalFilename,
      size: blob.size,
      blob,
    }
  }
  catch (err) {
    return handleError(err as string)
  }
}

// 浏览器下载
export function downloadBlob(blob: Blob, filename?: string) {
  if (!blob)
    return

  const downloadUrl = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.style.display = 'none'

  try {
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
  }
  finally {
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }
}

// 浏览器下载
export async function downloadFile(
  url: string,
  options: RequestOptions & { filename?: string } = {},
): Promise<void> {
  const result = await download(url, options)
  downloadBlob(result.blob, result.filename)
}
