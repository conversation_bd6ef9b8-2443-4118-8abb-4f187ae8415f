import type { Router, RouteRecordRaw } from 'vue-router'
import type { AppConfig } from '~/types/config'
import { createRouter, createWebHashHistory, createWebHistory } from 'vue-router'
import { routes } from 'vue-router/auto-routes'
import { useAuth } from '~/composables'

let router: ReturnType<typeof createRouter>
const title = useTitle()
const { getUserInfo, hasPerm } = useAuth()

export function setupRouter(config: AppConfig) {
  router = createRouter({
    history: config.routerMode === 'history'
      ? createWebHistory()
      : createWebHashHistory(),
    routes,
  })

  // 前置守卫
  router.beforeEach(async (to, from, next) => {
    const meta = to.meta
    title.value = meta?.title ? `${meta.title} - ${config.appName}` : config.appName
    // 公共页面直接放行
    if (meta?.public === true) {
      return next()
    }

    const user = await getUserInfo()
    if (!user.value) {
      return next({
        path: config.loginPath,
        query: { redirect: to.fullPath },
      })
    }

    // 检查路由权限
    if (meta?.perm) {
      if (!hasPerm(meta?.perm as string))
        return next('/common/403')
    }

    next()
  })

  return router
}

/**
 * 获取路由实例
 */
export function getRouter(): Router {
  return router
}

/**
 * 菜单项
 */
export interface MenuItem {
  title?: string
  icon?: string
  size?: number
  perm?: string
  name: string
  path: string
  order?: number
  children?: MenuItem[]
}

/**
 * 获取路由树
 */
export function getRoutesTree(): MenuItem[] {
  return routesToMenuTree(routes)
}

/**
 * 转换路由为树级菜单结构
 */
export function routesToMenuTree(routes: RouteRecordRaw[]): MenuItem[] {
  if (!Array.isArray(routes))
    return []

  const processNode = (node: RouteRecordRaw): MenuItem | MenuItem[] | null => {
    // 目录节点
    const index = node.children?.find(o => o.path === '')
    if (index) {
      if (!index.meta?.menu)
        return null

      // 处理子节点
      const children = (node.children || [])
        .filter(o => o.path !== '')
        .map(processNode)
        .flat()
        .filter(Boolean)
        .sort((a, b) => (a?.order ?? 0) - (b?.order ?? 0)) as MenuItem[]

      // 提升meta信息
      const menu = {
        ...index.meta,
        path: node.path,
        name: index.name?.toString() ?? '',
        children: children.length > 0 ? children : undefined,
      }

      // index本身就是叶子节点，将chilren提升，提升后删除自身下面的children
      if (index.meta?.leaf !== false) {
        delete menu.children
        return [menu, ...children]
      }

      return menu
    }

    // 菜单节点
    return node.meta?.menu
      ? {
          ...node.meta,
          path: node.path,
          name: node.name?.toString() ?? '',
        }
      : null
  }

  return routes
    .map(processNode)
    .flat()
    .filter(Boolean)
    .sort((a, b) => (a?.order ?? 0) - (b?.order ?? 0)) as MenuItem[]
}
