import dayjs from 'dayjs'

/**
 * 格式化日期时间字符串
 */
export function formatDateTime(
  input: string | Date | dayjs.Dayjs | null | undefined,
  format: string = 'YYYY-MM-DD HH:mm:ss',
  defaultValue: string = '',
): string {
  if (!input)
    return defaultValue

  const val = dayjs(input)
  if (!val.isValid())
    return defaultValue

  return val.format(format)
}

/**
 * 格式化为日期YYYY-MM-DD
 */
export function formatDate(
  input: string | Date | dayjs.Dayjs | null | undefined,
  defaultValue: string = '',
): string {
  return formatDateTime(input, 'YYYY-MM-DD', defaultValue)
}

/**
 * 格式化为日期时分 YYYY-MM-DD HH:mm
 */
export function formatDateShortTime(
  input: string | Date | dayjs.Dayjs | null | undefined,
  defaultValue: string = '',
): string {
  return formatDateTime(input, 'YYYY-MM-DD HH:mm', defaultValue)
}

/**
 * Element Plus表格日期单元格格式化
 */
export function formatElCellDate(row: any, column: any, cellValue: any) {
  return formatDate(cellValue)
}

/**
 * Element Plus表格日期单元格格式化
 */
export function formatElCellDateTime(row: any, column: any, cellValue: any) {
  return formatDateTime(cellValue)
}
