@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $header: (
    padding: 0,
  ),
  // $font-size: (
  //   'extra-large': 22px,
  //   'large': 19px,
  //   'medium': 17px,
  //   'base': 15px,
  //   'small': 13px,
  //   'extra-small': 11px,
  // ),
  $colors: (
      'primary': (
        'base': #048a7f,
      ),
      'success': (
        'base': #44b60c,
      ),
      'warning': (
        'base': #fa8c16,
      ),
      'danger': (
        'base': #ff4d4f,
      ),
      'error': (
        'base': #ff4d4f,
      ),
      'info': (
        'base': #05b1a5,
      ),
    )
);

// 引入Element Plus的样式
@use 'element-plus/theme-chalk/src/index.scss' as *;

// 暗黑模式
@use 'element-plus/theme-chalk/src/dark/css-vars.scss' as *;

// 重置一些默认样式
.el-tree {
  --el-tree-node-content-height: 30px;
}
