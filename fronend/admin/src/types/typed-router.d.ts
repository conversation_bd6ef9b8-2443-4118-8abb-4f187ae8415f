/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/': RouteRecordInfo<'/', '/', Record<never, never>, Record<never, never>>,
    '/after/': RouteRecordInfo<'/after/', '/after', Record<never, never>, Record<never, never>>,
    '/after/audit/': RouteRecordInfo<'/after/audit/', '/after/audit', Record<never, never>, Record<never, never>>,
    '/after/chief/': RouteRecordInfo<'/after/chief/', '/after/chief', Record<never, never>, Record<never, never>>,
    '/after/pileup/': RouteRecordInfo<'/after/pileup/', '/after/pileup', Record<never, never>, Record<never, never>>,
    '/after/report/': RouteRecordInfo<'/after/report/', '/after/report', Record<never, never>, Record<never, never>>,
    '/before/': RouteRecordInfo<'/before/', '/before', Record<never, never>, Record<never, never>>,
    '/before/basic/': RouteRecordInfo<'/before/basic/', '/before/basic', Record<never, never>, Record<never, never>>,
    '/before/basic/change/': RouteRecordInfo<'/before/basic/change/', '/before/basic/change', Record<never, never>, Record<never, never>>,
    '/before/basic/charge/': RouteRecordInfo<'/before/basic/charge/', '/before/basic/charge', Record<never, never>, Record<never, never>>,
    '/before/basic/register/': RouteRecordInfo<'/before/basic/register/', '/before/basic/register', Record<never, never>, Record<never, never>>,
    '/before/insuran/': RouteRecordInfo<'/before/insuran/', '/before/insuran', Record<never, never>, Record<never, never>>,
    '/before/insuran/basic/': RouteRecordInfo<'/before/insuran/basic/', '/before/insuran/basic', Record<never, never>, Record<never, never>>,
    '/before/team/': RouteRecordInfo<'/before/team/', '/before/team', Record<never, never>, Record<never, never>>,
    '/before/team/order/': RouteRecordInfo<'/before/team/order/', '/before/team/order', Record<never, never>, Record<never, never>>,
    '/before/team/query/': RouteRecordInfo<'/before/team/query/', '/before/team/query', Record<never, never>, Record<never, never>>,
    '/common/403': RouteRecordInfo<'/common/403', '/common/403', Record<never, never>, Record<never, never>>,
    '/common/notice/': RouteRecordInfo<'/common/notice/', '/common/notice', Record<never, never>, Record<never, never>>,
    '/common/profile/': RouteRecordInfo<'/common/profile/', '/common/profile', Record<never, never>, Record<never, never>>,
    '/common/profile/components/Base': RouteRecordInfo<'/common/profile/components/Base', '/common/profile/components/Base', Record<never, never>, Record<never, never>>,
    '/common/profile/components/Log': RouteRecordInfo<'/common/profile/components/Log', '/common/profile/components/Log', Record<never, never>, Record<never, never>>,
    '/common/profile/components/Sign': RouteRecordInfo<'/common/profile/components/Sign', '/common/profile/components/Sign', Record<never, never>, Record<never, never>>,
    '/data/': RouteRecordInfo<'/data/', '/data', Record<never, never>, Record<never, never>>,
    '/data/project/': RouteRecordInfo<'/data/project/', '/data/project', Record<never, never>, Record<never, never>>,
    '/data/project/add/': RouteRecordInfo<'/data/project/add/', '/data/project/add', Record<never, never>, Record<never, never>>,
    '/data/project/cacel/': RouteRecordInfo<'/data/project/cacel/', '/data/project/cacel', Record<never, never>, Record<never, never>>,
    '/data/total/': RouteRecordInfo<'/data/total/', '/data/total', Record<never, never>, Record<never, never>>,
    '/data/total/order/': RouteRecordInfo<'/data/total/order/', '/data/total/order', Record<never, never>, Record<never, never>>,
    '/finance/': RouteRecordInfo<'/finance/', '/finance', Record<never, never>, Record<never, never>>,
    '/finance/bill/': RouteRecordInfo<'/finance/bill/', '/finance/bill', Record<never, never>, Record<never, never>>,
    '/finance/invoice/': RouteRecordInfo<'/finance/invoice/', '/finance/invoice', Record<never, never>, Record<never, never>>,
    '/health/': RouteRecordInfo<'/health/', '/health', Record<never, never>, Record<never, never>>,
    '/health/basic/': RouteRecordInfo<'/health/basic/', '/health/basic', Record<never, never>, Record<never, never>>,
    '/health/basic/docTemplate/': RouteRecordInfo<'/health/basic/docTemplate/', '/health/basic/docTemplate', Record<never, never>, Record<never, never>>,
    '/health/basic/icdDict/': RouteRecordInfo<'/health/basic/icdDict/', '/health/basic/icdDict', Record<never, never>, Record<never, never>>,
    '/health/basic/medical/': RouteRecordInfo<'/health/basic/medical/', '/health/basic/medical', Record<never, never>, Record<never, never>>,
    '/health/basic/sampleType/': RouteRecordInfo<'/health/basic/sampleType/', '/health/basic/sampleType', Record<never, never>, Record<never, never>>,
    '/health/basic/signDict/': RouteRecordInfo<'/health/basic/signDict/', '/health/basic/signDict', Record<never, never>, Record<never, never>>,
    '/health/project/': RouteRecordInfo<'/health/project/', '/health/project', Record<never, never>, Record<never, never>>,
    '/health/project/examProject/': RouteRecordInfo<'/health/project/examProject/', '/health/project/examProject', Record<never, never>, Record<never, never>>,
    '/health/project/feeProject/': RouteRecordInfo<'/health/project/feeProject/', '/health/project/feeProject', Record<never, never>, Record<never, never>>,
    '/health/project/pack/': RouteRecordInfo<'/health/project/pack/', '/health/project/pack', Record<never, never>, Record<never, never>>,
    '/health/project/projectDict/': RouteRecordInfo<'/health/project/projectDict/', '/health/project/projectDict', Record<never, never>, Record<never, never>>,
    '/health/scheduling/': RouteRecordInfo<'/health/scheduling/', '/health/scheduling', Record<never, never>, Record<never, never>>,
    '/in/': RouteRecordInfo<'/in/', '/in', Record<never, never>, Record<never, never>>,
    '/in/check/': RouteRecordInfo<'/in/check/', '/in/check', Record<never, never>, Record<never, never>>,
    '/in/check/check/': RouteRecordInfo<'/in/check/check/', '/in/check/check', Record<never, never>, Record<never, never>>,
    '/in/check/image/': RouteRecordInfo<'/in/check/image/', '/in/check/image', Record<never, never>, Record<never, never>>,
    '/in/check/sign/': RouteRecordInfo<'/in/check/sign/', '/in/check/sign', Record<never, never>, Record<never, never>>,
    '/in/test/': RouteRecordInfo<'/in/test/', '/in/test', Record<never, never>, Record<never, never>>,
    '/in/test/collect/': RouteRecordInfo<'/in/test/collect/', '/in/test/collect', Record<never, never>, Record<never, never>>,
    '/in/test/result/': RouteRecordInfo<'/in/test/result/', '/in/test/result', Record<never, never>, Record<never, never>>,
    '/in/test/submit/': RouteRecordInfo<'/in/test/submit/', '/in/test/submit', Record<never, never>, Record<never, never>>,
    '/login': RouteRecordInfo<'/login', '/login', Record<never, never>, Record<never, never>>,
    '/sys/': RouteRecordInfo<'/sys/', '/sys', Record<never, never>, Record<never, never>>,
    '/sys/basic/': RouteRecordInfo<'/sys/basic/', '/sys/basic', Record<never, never>, Record<never, never>>,
    '/sys/basic/barCodeConfig/': RouteRecordInfo<'/sys/basic/barCodeConfig/', '/sys/basic/barCodeConfig', Record<never, never>, Record<never, never>>,
    '/sys/basic/dict/': RouteRecordInfo<'/sys/basic/dict/', '/sys/basic/dict', Record<never, never>, Record<never, never>>,
    '/sys/basic/file/': RouteRecordInfo<'/sys/basic/file/', '/sys/basic/file', Record<never, never>, Record<never, never>>,
    '/sys/basic/idBuilder/': RouteRecordInfo<'/sys/basic/idBuilder/', '/sys/basic/idBuilder', Record<never, never>, Record<never, never>>,
    '/sys/basic/setting/': RouteRecordInfo<'/sys/basic/setting/', '/sys/basic/setting', Record<never, never>, Record<never, never>>,
    '/sys/basic/setting/components/App': RouteRecordInfo<'/sys/basic/setting/components/App', '/sys/basic/setting/components/App', Record<never, never>, Record<never, never>>,
    '/sys/basic/setting/components/HospitalInfo': RouteRecordInfo<'/sys/basic/setting/components/HospitalInfo', '/sys/basic/setting/components/HospitalInfo', Record<never, never>, Record<never, never>>,
    '/sys/basic/setting/components/Safe': RouteRecordInfo<'/sys/basic/setting/components/Safe', '/sys/basic/setting/components/Safe', Record<never, never>, Record<never, never>>,
    '/sys/basic/smsConfig/': RouteRecordInfo<'/sys/basic/smsConfig/', '/sys/basic/smsConfig', Record<never, never>, Record<never, never>>,
    '/sys/dept/': RouteRecordInfo<'/sys/dept/', '/sys/dept', Record<never, never>, Record<never, never>>,
    '/sys/dept/components/DeptPost': RouteRecordInfo<'/sys/dept/components/DeptPost', '/sys/dept/components/DeptPost', Record<never, never>, Record<never, never>>,
    '/sys/dept/components/Perm': RouteRecordInfo<'/sys/dept/components/Perm', '/sys/dept/components/Perm', Record<never, never>, Record<never, never>>,
    '/sys/dept/dept/': RouteRecordInfo<'/sys/dept/dept/', '/sys/dept/dept', Record<never, never>, Record<never, never>>,
    '/sys/dept/perm/': RouteRecordInfo<'/sys/dept/perm/', '/sys/dept/perm', Record<never, never>, Record<never, never>>,
    '/sys/dept/perm/components/Post': RouteRecordInfo<'/sys/dept/perm/components/Post', '/sys/dept/perm/components/Post', Record<never, never>, Record<never, never>>,
    '/sys/dept/perm/components/User': RouteRecordInfo<'/sys/dept/perm/components/User', '/sys/dept/perm/components/User', Record<never, never>, Record<never, never>>,
    '/sys/dept/user/': RouteRecordInfo<'/sys/dept/user/', '/sys/dept/user', Record<never, never>, Record<never, never>>,
    '/sys/it/': RouteRecordInfo<'/sys/it/', '/sys/it', Record<never, never>, Record<never, never>>,
    '/sys/it/job/': RouteRecordInfo<'/sys/it/job/', '/sys/it/job', Record<never, never>, Record<never, never>>,
    '/sys/it/job/components/Job': RouteRecordInfo<'/sys/it/job/components/Job', '/sys/it/job/components/Job', Record<never, never>, Record<never, never>>,
    '/sys/it/job/components/Log': RouteRecordInfo<'/sys/it/job/components/Log', '/sys/it/job/components/Log', Record<never, never>, Record<never, never>>,
    '/sys/it/log/': RouteRecordInfo<'/sys/it/log/', '/sys/it/log', Record<never, never>, Record<never, never>>,
    '/sys/it/log/audit': RouteRecordInfo<'/sys/it/log/audit', '/sys/it/log/audit', Record<never, never>, Record<never, never>>,
    '/sys/it/log/loginlog': RouteRecordInfo<'/sys/it/log/loginlog', '/sys/it/log/loginlog', Record<never, never>, Record<never, never>>,
    '/sys/it/runtime/': RouteRecordInfo<'/sys/it/runtime/', '/sys/it/runtime', Record<never, never>, Record<never, never>>,
  }
}
