export interface AppConfig {
  appName: string
  apiUrl: string
  routerMode: 'history' | 'hash'
  loginPath: string
  watermark: boolean
  pageSize: number[]
  beforeunloadWarning: boolean
  [prop: string]: any
}

// 默认配置
export const defaultConfig: AppConfig = {
  appName: '管理平台',
  routerMode: 'hash',
  apiUrl: 'http://127.0.0.1:8000',
  watermark: false,
  loginPath: '/login',
  pageSize: [20, 30, 50, 100, 200, 500],
  beforeunloadWarning: true,
}
