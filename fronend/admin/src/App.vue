<script setup lang="ts">
import { ElLoading } from 'element-plus'
import Header from '~/components/layouts/Header.vue'
import LeftSide from '~/components/layouts/LeftSide.vue'
import Tabs from '~/components/layouts/Tabs.vue'
import { isDark, useAuth, useTabs } from '~/composables'
import { getConfig } from '~/utils'

const route = useRoute()
const router = useRouter()
const { getUser } = useAuth()
const { keepAlives } = useTabs()

// 状态管理
const isInit = ref(false)

// 水印配置
const watermarkConfig = computed(() => ({
  content: route.meta?.watermark !== false && getConfig().watermark ? getUser()?.userName : '',
  font: {
    color: isDark.value ? 'rgba(255, 255, 255, .06)' : 'rgba(0, 0, 0, .06)',
  },
}))

// 为组件动态添加name属性，用于keep-alive缓存
function wrapComponent(component: any) {
  if (!component)
    return component

  if (component.type.name !== route.name)
    component.type.name = route.name
  return component
}

onMounted(async () => {
  // 初始化加载
  const loadding = ElLoading.service({
    lock: true,
  })
  await router.isReady()
  isInit.value = true
  loadding.close()
})
</script>

<template>
  <!-- 公共页面 -->
  <router-view v-if="route.meta?.public === true" />

  <!-- 主布局 -->
  <el-watermark
    v-else-if="isInit" :z-index="9999" :content="watermarkConfig.content" :font="watermarkConfig.font"
    class="h-screen"
  >
    <el-container class="h-screen flex flex-col">
      <el-header class="flex-shrink-0 p-0">
        <Header />
      </el-header>
      <el-container class="flex-1 overflow-hidden">
        <!-- 侧边栏 -->
        <el-aside class="h-full w-18 overflow-hidden transition-all duration-300 ease-in-out">
          <LeftSide />
        </el-aside>
        <el-main class="flex flex-col p-0">
          <div class="mt-1">
            <Tabs />
          </div>
          <el-scrollbar id="main-top" class="flex-1">
            <router-view v-slot="{ Component }">
              <keep-alive :include="keepAlives">
                <component :is="wrapComponent(Component)" />
              </keep-alive>
            </router-view>
          </el-scrollbar>
        </el-main>
      </el-container>
    </el-container>
  </el-watermark>
</template>
