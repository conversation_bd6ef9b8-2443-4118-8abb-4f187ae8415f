<script setup lang="ts">
import { ElMessageBox } from 'element-plus'
import { uploadImage } from '~/api/common/sys'
import * as api from '~/api/sys/user'
import { useDict } from '~/composables'
import { resetForm, tips } from '~/utils'
import DeptPost from '../components/DeptPost.vue'

definePage({
  meta: {
    title: '账户管理',
    perm: 'sys:user',
    menu: true,
    order: 3,
  },
})

// 状态及数据
const queryFormRef = useTemplateRef('queryFormRef')
const tableRef = useTemplateRef<any>('tableRef')
const formRef = useTemplateRef('formRef')
const { KEYS, getName } = useDict()

const dictCanAction = computed(() => tableRef.value?.hasSelected && !tableRef.value?.loading)
const currentDept = ref<any>()
const currentPost = ref<any>()
const getStatusDesc = getName(KEYS.USER_STATUS)
const getSexName = getName(KEYS.SEX)

const currentNode = computed(() => {
  return currentPost.value || currentDept.value || null
})

// 表单
const query = reactive({
  type: 'userName',
  key: '',
  status: undefined,
  deptId: currentDept.value?.id,
  postId: currentPost.value?.id,
})
const initFormData = {
  loginName: '',
  userName: '',
  sex: 1,
  hisCode: '',
  password: '',
  phone: '',
  email: '',
  status: 0,
  idNo: '',
  titleDictId: undefined,
  titleDictName: '',
  certificateNo: '',
  image: '',
  entryDate: undefined,
  categoryDictId: undefined,
  categoryDictName: '',
  deptPosts: [],
}

// 验证规则
const rules = {
  loginName: [{ required: true, message: '请输入登录账号' }],
  userName: [{ required: true, message: '请输入真实姓名' }],
}

// 监听组织变化
watchEffect(() => {
  query.deptId = currentDept.value?.id
  query.postId = currentPost.value?.id
  nextTick(() => {
    tableRef.value?.fetchData()
  })
})

// 删除
async function handleRemove(ids: number[]) {
  if (ids && ids.length) {
    await tableRef.value.withLoading(async () => {
      await api.remove(ids)
      tips.success()
      await tableRef.value.fetchData()
    })
  }
}

// 重置密码
function handleResetPwd(id: number) {
  api.resetPassword(id).then((res) => {
    ElMessageBox.alert(`已将登录密码重置为：${res.data}`, '提示')
  })
}

// 组织结构节点点击
function changeNode(node: any) {
  if (isPost(node)) {
    currentDept.value = undefined
    currentPost.value = node
  }
  else {
    currentDept.value = node
    currentPost.value = undefined
  }
}

// 图像上传
function handleUploadImage(file: File) {
  return uploadImage(file, '账户图像', 500)
}

// 图像上传成功
function handleUploadImageSuccess(result: any) {
  if (result.data) {
    formRef.value.formData.image = result.data
  }
}

// 是否为岗位数据
function isPost(data: any) {
  return data && data.key.startsWith('p')
}
</script>

<template>
  <el-scrollbar view-style="height: calc(100vh - 105px)">
    <el-splitter>
      <el-splitter-panel :min="200" size="250px" collapsible>
        <el-scrollbar>
          <div class="p-4 pt-6">
            <DeptPost @node-click="changeNode" />
          </div>
        </el-scrollbar>
      </el-splitter-panel>
      <el-splitter-panel :min="800">
        <el-scrollbar>
          <div class="p-4">
            <table-list ref="tableRef" :fetch-api="api.list" :query-params="query">
              <template #search>
                <el-form
                  ref="queryFormRef" :model="query" :inline="true" @submit.prevent="tableRef.search()"
                  @reset="resetForm(queryFormRef)"
                >
                  <el-form-item v-if="currentNode" class="mr-4!">
                    <el-tag closable size="large" @close="changeNode(undefined)">
                      {{ currentNode?.name }}
                    </el-tag>
                  </el-form-item>
                  <el-form-item prop="type" class="w-26 mr-1!">
                    <el-select v-model="query.type">
                      <el-option label="姓名" value="userName" />
                      <el-option label="账户" value="loginName" />
                      <el-option label="手机号" value="phone" />
                      <el-option label="邮箱" value="email" />
                      <el-option label="HIS编号" value="hisCode" />
                    </el-select>
                  </el-form-item>
                  <el-form-item prop="key" class="w-70">
                    <el-input v-model="query.key" placeholder="请输入搜索关键词" clearable>
                      <template #prefix>
                        <icon icon="carbon:search" />
                      </template>
                    </el-input>
                  </el-form-item>
                  <el-form-item prop="status" class="w-30" label="状态">
                    <dict-select v-model="query.status" :dict-key="KEYS.USER_STATUS" clearable placeholder="全部" />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" native-type="submit" :loading="tableRef?.loading">
                      <template #icon>
                        <icon icon="mdi:magnify" />
                      </template>
                      查询
                    </el-button>
                    <el-button native-type="reset">
                      <template #icon>
                        <icon icon="radix-icons:reset" />
                      </template>
                      重置
                    </el-button>
                  </el-form-item>
                </el-form>
              </template>
              <template #left-actions>
                <el-button v-if="$hasPerm('sys:user:add')" type="primary" @click="formRef.add()">
                  <template #icon>
                    <icon icon="ep:plus" />
                  </template>
                  新增
                </el-button>
                <el-button-group v-if="$hasPerm('sys:user:delete')">
                  <el-popconfirm title="确认要删除选中的项吗？" placement="bottom" @confirm="handleRemove(tableRef.selected)">
                    <template #reference>
                      <el-button title="删除" :disabled="!dictCanAction">
                        <template #icon>
                          <icon icon="ep:delete" />
                        </template>
                        删除
                      </el-button>
                    </template>
                  </el-popconfirm>
                </el-button-group>
              </template>
              <el-table-column type="selection" align="center" width="50" />
              <el-table-column prop="id" label="ID" align="center" width="100" />
              <el-table-column prop="userName" label="真实姓名" show-overflow-tooltip />
              <el-table-column prop="loginName" label="登录账号" show-overflow-tooltip />
              <el-table-column prop="sex" label="性别" width="80">
                <template #default="{ row }">
                  <el-tag :type="row.sex === 1 ? 'primary' : 'success'">
                    {{ getSexName(row.sex) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="titleDictName" label="职称" width="200" show-overflow-tooltip />
              <el-table-column prop="phone" label="手机" align="center" show-overflow-tooltip />
              <el-table-column prop="status" label="状态" align="center" width="120">
                <template #default="{ row }">
                  <el-tag :type="row.status === 0 ? 'success' : 'danger'">
                    {{ getStatusDesc(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="200">
                <template #default="{ row }">
                  <div class="flex justify-center space-x-3">
                    <el-button v-if="$hasPerm('sys:user:edit')" link @click="formRef.edit(row.id)">
                      编辑
                    </el-button>
                    <el-popconfirm v-if="$hasPerm('sys:user:resetPassword')" title="确认要重置该账号密码吗？" placement="left" @confirm="handleResetPwd(row.id)">
                      <template #reference>
                        <el-button link>
                          重置密码
                        </el-button>
                      </template>
                    </el-popconfirm>
                    <el-popconfirm v-if="$hasPerm('sys:user:delete')" title="确认要删除该项吗？" placement="left" @confirm="handleRemove([row.id])">
                      <template #reference>
                        <el-button type="warning" link>
                          删除
                        </el-button>
                      </template>
                    </el-popconfirm>
                  </div>
                </template>
              </el-table-column>
            </table-list>

            <!-- 用户编辑对话框 -->
            <detail-form ref="formRef" class="p-6" title="账户" :init-data="initFormData" :rules="rules" :get-api="api.getEdit" width="800px" :add-api="api.add" :edit-api="api.postEdit" @success="tableRef.fetchData()">
              <el-row :gutter="20">
                <el-col :span="18">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-form-item label="真实姓名" prop="userName">
                        <el-input v-model="formRef.formData.userName" placeholder="请输入真实姓名" maxlength="15" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="身份证号" prop="idNo">
                        <el-input v-model="formRef.formData.idNo" placeholder="请输入身份证号" maxlength="18" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-form-item label="登录账号" prop="loginName">
                        <el-input v-model="formRef.formData.loginName" placeholder="请输入登录账号" maxlength="15" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="性别" prop="sex">
                        <dict-select v-model="formRef.formData.sex" :dict-key="KEYS.SEX" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-form-item label="手机号码" prop="phone">
                        <el-input v-model="formRef.formData.phone" placeholder="请输入手机号码" maxlength="15" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="邮箱" prop="email">
                        <el-input v-model="formRef.formData.email" placeholder="请输入邮箱" maxlength="63" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-form-item label="HIS编号" prop="hisCode">
                        <el-input v-model="formRef.formData.hisCode" placeholder="请输入HIS编号" maxlength="15" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="状态" prop="status">
                        <dict-select v-model="formRef.formData.status" :dict-key="KEYS.USER_STATUS" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form-item label="科室/岗位">
                        <dept-select v-model="formRef.formData.deptPosts" value-field="key" multiple post />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-col>
                <!-- 图像区域 -->
                <el-col :span="6">
                  <el-form-item label="" prop="image">
                    <div class="w-full flex flex-col items-center">
                      <div class="mb-2 h-44 w-36 flex items-center justify-center border bg-gray-50 dark:bg-gray-900">
                        <el-image
                          v-if="formRef.formData.image"
                          class="h-full w-full rounded"
                          :src="formRef.formData.image"
                          :preview-src-list="[formRef.formData.image]"
                          hide-on-click-modal
                          fit="cover"
                        />
                        <div v-else class="flex flex-col items-center text-sm text-gray-400">
                          <icon icon="ep:picture" class="mb-2 text-2xl" />
                          <span>暂无图像</span>
                        </div>
                      </div>
                      <file-upload accept="image/*" :upload="handleUploadImage" @success="handleUploadImageSuccess">
                        <template #default="{ triggerUpload, loading: uploadLoading }">
                          <el-button link :loading="uploadLoading" @click="triggerUpload">
                            <template #icon>
                              <icon icon="material-symbols:upload-rounded" />
                            </template>
                            {{ formRef.formData.image ? '重新上传' : '上传图像' }}
                          </el-button>
                        </template>
                      </file-upload>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20" class="mt-4">
                <el-col :span="12">
                  <el-form-item label="职称" prop="titleDictId">
                    <dict-select v-model:name="formRef.formData.titleDictName" v-model="formRef.formData.titleDictId" :dict-key="KEYS.DICT.MEDICAL_TITLE" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="证书编号" prop="certificateNo">
                    <el-input v-model="formRef.formData.certificateNo" placeholder="请输入证书编号" maxlength="31" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="人员类别" prop="categoryDictId">
                    <dict-select v-model:name="formRef.formData.categoryDictName" v-model="formRef.formData.categoryDictId" :dict-key="KEYS.DICT.MEDICAL_TYPE" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="入职日期" prop="entryDate">
                    <el-date-picker v-model="formRef.formData.entryDate" type="date" placeholder="请选择入职日期" class="w-full!" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="简介" prop="intro">
                    <el-input v-model="formRef.formData.intro" :rows="3" type="textarea" placeholder="请输入简介" />
                  </el-form-item>
                </el-col>
              </el-row>
            </detail-form>
          </div>
        </el-scrollbar>
      </el-splitter-panel>
    </el-splitter>
  </el-scrollbar>
</template>

<style scoped lang="scss">
:deep(.el-splitter-bar__dragger-horizontal::before) {
  width: 1px;
}
</style>
