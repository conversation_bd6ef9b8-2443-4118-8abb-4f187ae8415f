<script setup lang="ts">
import * as api from '~/api/sys/perm'
import { useLoading } from '~/composables'
import { tips } from '~/utils'
import Perm from '../../components/Perm.vue'

// 状态
const editUser = ref<any>({})
const visible = ref(false)
const activeTab = ref('info')
const permDetail = ref<any>({})
const permInfoRef = useTemplateRef('permInfoRef')
const permDenyRef = useTemplateRef('permDenyRef')
const permAllowRef = useTemplateRef('permAllowRef')
const { loading, withLoading } = useLoading()
const { loading: submiting, withLoading: withSubmitting } = useLoading()

const dialogTitle = computed(() => {
  return `用户权限设置 - ${editUser.value.userName}`
})

// 提交
async function submit() {
  await withSubmitting(async () => {
    const allows = permAllowRef.value?.getCheckedKeys()
    const denys = permDenyRef.value?.getCheckedKeys()
    await api.postUserPerm(editUser.value.id, { allows, denys })
    tips.success()
    visible.value = false
  })
}

// 打开
async function open(user: any) {
  editUser.value = user
  visible.value = true
  activeTab.value = 'info'
  permDetail.value = {}
  permInfoRef.value?.setCheckedKeys([])
  permDenyRef.value?.setCheckedKeys([])
  permAllowRef.value?.setCheckedKeys([])
  await withLoading(async () => {
    const res = await api.getUserPerm(user.id)
    permDetail.value = res.data
    permInfoRef.value?.setCheckedKeys(res.data.postPerms || [])
    permAllowRef.value?.setCheckedKeys(res.data.allows || [])
    permDenyRef.value?.setCheckedKeys(res.data.denys || [])
  })
}

defineExpose({
  open,
})
</script>

<template>
  <el-dialog
    v-model="visible" :title="dialogTitle" width="550" draggable
    :close-on-click-modal="false" :close-on-press-escape="false"
  >
    <el-alert title="用户最终的权限为：所有岗位权限 + 授予权限 - 排除权限" type="info" show-icon />
    <el-tabs v-model="activeTab" v-loading="loading" class="mt-1">
      <el-tab-pane label="岗位权限" name="info">
        <div class="flex">
          <div>用户岗位：</div>
          <div v-if="permDetail.posts && permDetail.posts.length" class="flex-1 space-x-2">
            <el-tag v-for="item in permDetail.posts" :key="item">
              {{ item }}
            </el-tag>
          </div>
          <div v-else>
            无岗位
          </div>
        </div>
        <Perm ref="permInfoRef" class="mt-4" readonly />
      </el-tab-pane>
      <el-tab-pane label="授予权限" name="allow">
        <Perm ref="permAllowRef" />
      </el-tab-pane>
      <el-tab-pane label="排除权限" name="deny">
        <Perm ref="permDenyRef" />
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="flex justify-end space-x-3">
        <el-button :disabled="submiting" @click="visible = false">
          取消
        </el-button>
        <el-button type="primary" :loading="submiting" :disabled="loading" @click="submit">
          <template #icon>
            <icon icon="ep:check" />
          </template>
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
