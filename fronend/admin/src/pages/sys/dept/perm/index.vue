<script setup lang="ts">
import * as api from '~/api/sys/perm'
import { resetForm } from '~/utils'
import DeptPost from '../components/DeptPost.vue'
import PostPerm from './components/Post.vue'
import UserPerm from './components/User.vue'

definePage({
  meta: {
    title: '权限管理',
    perm: 'sys:perm',
    menu: true,
    order: 4,
  },
})

// 状态及数据
const queryFormRef = useTemplateRef('queryFormRef')
const tableRef = useTemplateRef('tableRef')
const postPermRef = useTemplateRef('postPermRef')
const userPermRef = useTemplateRef('userPermRef')
const currentDept = ref<any>()
const currentPost = ref<any>()
const currentNode = computed(() => {
  return currentPost.value || currentDept.value || null
})

// 表单
const query = reactive({
  type: 'userName',
  key: '',
  deptId: currentDept.value?.id,
  postId: currentPost.value?.id,
})

// 监听组织变化
watchEffect(() => {
  query.deptId = currentDept.value?.id
  query.postId = currentPost.value?.id
  nextTick(() => {
    tableRef.value?.fetchData()
  })
})

// 组织结构节点点击
function changeNode(node: any) {
  if (isPost(node)) {
    currentDept.value = undefined
    currentPost.value = node
  }
  else {
    currentDept.value = node
    currentPost.value = undefined
  }
}

// 权限设置
async function handlePerm(type: 'post' | 'user', data: any) {
  if (type === 'post')
    await postPermRef.value?.open(data)
  else
    await userPermRef.value?.open(data)
}

// 是否为岗位数据
function isPost(data: any) {
  return data && data.key.startsWith('p')
}
</script>

<template>
  <el-scrollbar view-style="height: calc(100vh - 105px)">
    <el-splitter>
      <el-splitter-panel :min="200" size="260px" collapsible>
        <el-scrollbar>
          <div class="p-4 pt-6">
            <DeptPost @node-click="changeNode">
              <template #default="{ node }">
                <el-button v-if="isPost(node) && $hasPerm('sys:perm:post')" link @click.stop="handlePerm('post', node)">
                  权限
                </el-button>
              </template>
            </DeptPost>
          </div>
        </el-scrollbar>
      </el-splitter-panel>
      <el-splitter-panel :min="900">
        <el-scrollbar>
          <div class="p-4">
            <table-list ref="tableRef" :fetch-api="api.userList" :query-params="query">
              <template #left-actions>
                <el-form
                  ref="queryFormRef" :model="query" :inline="true" @submit.prevent="tableRef.search()"
                  @reset="resetForm(queryFormRef)"
                >
                  <el-form-item v-if="currentNode" class="mr-4!">
                    <el-tag closable size="large" @close="changeNode(undefined)">
                      {{ currentNode?.name }}
                    </el-tag>
                  </el-form-item>
                  <el-form-item prop="type" class="w-26 mr-1!">
                    <el-select v-model="query.type">
                      <el-option label="姓名" value="userName" />
                      <el-option label="账户" value="loginName" />
                      <el-option label="手机号" value="phone" />
                      <el-option label="邮箱" value="email" />
                    </el-select>
                  </el-form-item>
                  <el-form-item prop="key" class="w-70">
                    <el-input v-model="query.key" placeholder="请输入搜索关键词" clearable>
                      <template #prefix>
                        <icon icon="carbon:search" />
                      </template>
                    </el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" native-type="submit" :loading="tableRef?.loading">
                      <template #icon>
                        <icon icon="mdi:magnify" />
                      </template>
                      查询
                    </el-button>
                    <el-button native-type="reset">
                      <template #icon>
                        <icon icon="radix-icons:reset" />
                      </template>
                      重置
                    </el-button>
                  </el-form-item>
                </el-form>
              </template>
              <el-table-column prop="id" label="ID" align="center" width="100" />
              <el-table-column prop="userName" label="真实姓名" width="180" show-overflow-tooltip />
              <el-table-column prop="loginName" label="登录账号" width="180" show-overflow-tooltip />
              <el-table-column prop="titleDictName" label="职称" width="180" />
              <el-table-column prop="phone" label="手机" align="center" width="200" show-overflow-tooltip />
              <el-table-column prop="email" label="邮件" show-overflow-tooltip />
              <el-table-column label="操作" align="center" width="100">
                <template #default="{ row }">
                  <el-button v-if="$hasPerm('sys:perm:user')" link @click="handlePerm('user', row)">
                    权限
                  </el-button>
                </template>
              </el-table-column>
            </table-list>
          </div>
        </el-scrollbar>
      </el-splitter-panel>
    </el-splitter>
  </el-scrollbar>
  <!-- 权限 -->
  <PostPerm ref="postPermRef" />
  <UserPerm ref="userPermRef" />
</template>

<style scoped lang="scss">
:deep(.el-splitter-bar__dragger-horizontal::before) {
  width: 1px;
}
</style>
