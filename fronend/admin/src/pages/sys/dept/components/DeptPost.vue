<script setup lang="ts">
import type { TreeInstance } from 'element-plus'
import { getCommonDeptTree } from '~/api/sys/dept'
import { useLoading } from '~/composables'

// 事件
const emit = defineEmits<{
  (e: 'nodeClick', data: any): void
}>()

// 状态数据
const treeData = ref<any[]>([])
const currentNode = ref<any>(null)
const filterText = ref('')

const treeRef = useTemplateRef<TreeInstance>('treeRef')
const { loading, withLoading } = useLoading()

// 加载树
async function loadTree() {
  await withLoading(async () => {
    const res = await getCommonDeptTree({ post: true })
    treeData.value = res.data
  })
}

// 节点选中
function handleNodeClick(data: any) {
  currentNode.value = data
  emit('nodeClick', data)
}

// 树过滤
watchEffect(() => {
  treeRef.value?.filter(filterText.value)
})
function filterNode(value: string, data: any) {
  value = value?.trim()?.toLowerCase()
  if (!value)
    return true

  return data.name.toLowerCase().includes(value) || (data.data && (
    (data.data.py && data.data.py.toLowerCase().includes(value))
    || (data.data.code && data.data.code.toLowerCase().includes(value))
    || (data.data.py && data.data.py.toLowerCase().includes(value))
  ))
}

// 是否为岗位数据
function isPost(data: any) {
  return data.key.startsWith('p')
}

// 初始化
onMounted(() => {
  loadTree()
})
</script>

<template>
  <div class="flex flex-col">
    <!-- 工具栏 -->
    <div class="flex items-center justify-between">
      <el-input v-model="filterText" placeholder="输入关键词过滤" class="w-full" clearable>
        <template #prefix>
          <icon icon="carbon:search" />
        </template>
      </el-input>
      <el-button circle :loading="loading" title="刷新" class="ml-3" @click="loadTree">
        <template #icon>
          <icon icon="mdi:refresh" />
        </template>
      </el-button>
    </div>

    <!-- 树形列表 -->
    <div v-loading="loading" class="flex-1">
      <el-tree
        ref="treeRef" class="tree mt-3" :data="treeData" :props="{ label: 'name', children: 'children' }"
        :filter-node-method="filterNode" node-key="key" default-expand-all highlight-current check-on-click-node :expand-on-click-node="false"
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <div class="w-[calc(100%-28px)] flex items-center justify-between">
            <div class="flex-1 truncate">
              <icon v-if="data.data.type === 1" icon="ph:buildings" title="公司" />
              <icon v-else-if="isPost(data)" icon="mdi:account-tie" title="岗位" />
              <icon v-else icon="fluent-mdl2:org" title="科室" />
              <span class="ml-1" :class="{ 'line-through': data.data.disabled }">{{ node.label }}</span>
            </div>
            <slot :node="data" />
          </div>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<style scoped lang="scss">
.tree {
  :deep(.el-tree-node__content) {
    padding: 4px 2px;
    border-radius: 4px;
    overflow: hidden;
  }
}
</style>
