<script setup lang="ts">
import * as api from '~/api/sys/smsConfig'
import { resetForm, tips } from '~/utils'

definePage({
  meta: {
    title: '短信配置',
    perm: 'sys:smsConfig',
    keepAlive: true,
    menu: true,
    order: 3,
  },
})

// 状态及数据
const tableRef = useTemplateRef<any>('tableRef')
const formRef = useTemplateRef('formRef')
const queryFormRef = useTemplateRef('queryFormRef')
const query = reactive({
  type: 'contentTemplate',
  key: '',
  status: undefined,
})
const canAction = computed(() => tableRef.value?.hasSelected && !tableRef.value?.loading)

// 操作任务
async function handleBy(ids: number[], fn: any) {
  if (ids && ids.length) {
    await tableRef.value.withLoading(async () => {
      await fn(ids)
      tips.success()
      await tableRef.value.fetchData()
    })
  }
}

// 启用
async function handleEnable(ids: number[]) {
  await handleBy(ids, api.enable)
}

// 禁用
async function handleDisable(ids: number[]) {
  await handleBy(ids, api.disable)
}

// 表单
const initsmsConfigData = {
  messageId: '',
  supplierTemplateCode: '',
  contentTemplate: '',
  sort: 0,
}
</script>

<template>
  <div class="p-4">
    <table-list ref="tableRef" :fetch-api="api.list" :query-params="query">
      <template #search>
        <el-form ref="queryFormRef" :model="query" :inline="true" @submit.prevent="tableRef.search()" @reset="resetForm(queryFormRef)">
          <el-form-item prop="type" class="w-28 mr-1!">
            <el-select v-model="query.type">
              <el-option label="模板内容" value="contentTemplate" />
              <el-option label="提供商编号" value="supplierTemplateCode" />
              <el-option label="消息ID" value="messageId" />
            </el-select>
          </el-form-item>
          <el-form-item prop="key" class="w-60">
            <el-input v-model="query.key" placeholder="请输入搜索关键词" clearable>
              <template #prefix>
                <icon icon="carbon:search" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="status" label="状态" class="w-36">
            <el-select v-model="query.status" clearable>
              <el-option label="启用" :value="true" />
              <el-option label="禁用" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit" :loading="tableRef?.loading">
              <template #icon>
                <icon icon="mdi:magnify" />
              </template>
              查询
            </el-button>
            <el-button native-type="reset">
              <template #icon>
                <icon icon="radix-icons:reset" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #left-actions>
        <el-button-group>
          <el-button v-if="$hasPerm('sys:smsConfig:enable')" title="启用" :disabled="!canAction" @click="handleEnable(tableRef.selected)">
            <template #icon>
              <icon icon="codicon:debug-start" />
            </template>
            启用
          </el-button>
          <el-popconfirm v-if="$hasPerm('sys:smsConfig:disable')" title="确认要禁用选中短信吗？" placement="bottom" @confirm="handleDisable(tableRef.selected)">
            <template #reference>
              <el-button title="禁用选中" :disabled="!canAction">
                <template #icon>
                  <icon icon="ant-design:stop-twotone" />
                </template>
                禁用
              </el-button>
            </template>
          </el-popconfirm>
        </el-button-group>
      </template>
      <el-table-column type="selection" align="center" width="50" />
      <el-table-column prop="id" label="ID" align="center" width="100" />
      <el-table-column prop="messageId" label="消息ID" width="150" />
      <el-table-column prop="supplierTemplateCode" label="提供商编号" align="center" width="180" />
      <el-table-column prop="contentTemplate" label="默认模板内容" show-overflow-tooltip />
      <el-table-column prop="sort" align="center" label="排序值" width="100" />      />
      <el-table-column prop="disabled" label="状态" align="center" width="90">
        <template #default="{ row }">
          <el-tag :type="row.disabled ? 'danger' : 'success'">
            {{ row.disabled ? '禁用' : '启用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template #default="{ row }">
          <div class="space-x-2">
            <el-button v-if="$hasPerm('sys:smsConfig:edit')" link @click="formRef.edit(row.id)">
              编辑
            </el-button>
            <el-button v-if="$hasPerm('sys:smsConfig:enable') && row.disabled" type="success" link @click="handleEnable([row.id])">
              启用
            </el-button>
            <el-popconfirm v-else-if="$hasPerm('sys:smsConfig:disable')" title="确认要禁用该短信吗？" placement="left" @confirm="handleDisable([row.id])">
              <template #reference>
                <el-button type="warning" link>
                  禁用
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </table-list>
  </div>

  <!-- 编辑对话框 -->
  <detail-form
    ref="formRef" class="p-6" title="短信配置" :init-data="initsmsConfigData"
    :get-api="api.getEdit" :edit-api="api.postEdit" @success="tableRef.fetchData()"
  >
    <el-form-item label="提供商编号" prop="supplierTemplateCode">
      <el-input v-model="formRef.formData.supplierTemplateCode" placeholder="请输入提供商编号" maxlength="15" />
    </el-form-item>
    <el-form-item label="消息ID" prop="messageId">
      <el-input v-model="formRef.formData.messageId" disabled />
    </el-form-item>
    <el-form-item label="默认模板内容" prop="contentTemplate">
      <el-input v-model="formRef.formData.contentTemplate" type="textarea" :rows="5" disabled />
    </el-form-item>
    <el-form-item label="排序" prop="sort">
      <el-input-number v-model="formRef.formData.sort" :min="0" />
    </el-form-item>
  </detail-form>
</template>
