<script setup lang="ts">
import type { UploadUserFile } from 'element-plus'
import * as api from '~/api/sys/upfile'
import { formatBytesSize, formatDate, formatElCellDateTime, resetForm, tips } from '~/utils'

definePage({
  meta: {
    title: '文件管理',
    perm: 'sys:upfile',
    menu: true,
    order: 8,
  },
})

// 状态及数据
const query = reactive({
  type: 'fileName',
  key: '',
  startTime: '',
  endTime: '',
  timeRange: undefined as any,
})
const formRef = useTemplateRef('formRef')
const tableRef = useTemplateRef<any>('tableRef')
const queryParams = computed(() => {
  return {
    ...query,
    timeRange: undefined,
  }
})
const canAction = computed(() => tableRef.value?.hasSelected && !tableRef.value?.loading)

const uploadRef = useTemplateRef('uploadRef')
const uploading = ref(false)
const uploadDialogVisible = ref(false)
const previewDialogVisible = ref(false)
const previewUrl = ref('')
const previewMode = ref<'image' | 'pdf'>('image')

// 监听变更
watch(() => query.timeRange, (newVal) => {
  if (newVal) {
    query.startTime = formatDate(newVal[0] as Date)
    query.endTime = formatDate(newVal[1] as Date)
  }
  else {
    query.startTime = ''
    query.endTime = ''
  }
})

// 删除
async function handleRemove(ids: number[]) {
  if (ids && ids.length) {
    await tableRef.value.withLoading(async () => {
      await api.removePhysical(ids)
      tips.success()
      await tableRef.value.fetchData()
    })
  }
}

// 上传
function handleUpload() {
  if (uploadRef.value?.submit())
    uploading.value = true
}

// 取消上传
function handleCanclelUpload() {
  uploadRef.value?.clearFiles()
  uploading.value = false
  uploadDialogVisible.value = false
}

// 上传完成
function uploadComplete(fileList: UploadUserFile[]) {
  uploading.value = false
  uploadDialogVisible.value = false
  if (fileList?.length)
    tips.success(`已成功上${fileList.length}传个文件`)
  uploadRef.value?.clearFiles()
  tableRef.value.fetchData()
}

// 上传失败
function uploadFail() {
  uploading.value = false
}

// 预览
function handlePreview(row: any) {
  const { fullPath } = row
  const ext = fullPath.split('.').pop().toLowerCase()
  const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)
  // const isPdf = ['pdf'].includes(ext)

  if (isImage) {
    previewMode.value = isImage ? 'image' : 'pdf'
    previewUrl.value = fullPath
    previewDialogVisible.value = true
  }
  else {
    tips.error('暂不支持该文件类型预览，请下载查看')
  }
}

// 下载
function handleDownload(id: number) {
  return api.download(id)
}

// 格式化文件大小
function formatFileSize(row: any, column: any, cellValue: any) {
  return formatBytesSize(cellValue)
}
</script>

<template>
  <div class="p-4">
    <table-list ref="tableRef" :fetch-api="api.list" :query-params="queryParams">
      <template #search>
        <el-form
          ref="formRef" :model="query" :inline="true" @submit.prevent="tableRef.search()"
          @reset="resetForm(formRef)"
        >
          <el-form-item prop="type" class="w-26 mr-1!">
            <el-select v-model="query.type">
              <el-option label="文件名称" value="fileName" />
              <el-option label="描述说明" value="info" />
              <el-option label="扩展名" value="fileExt" />
              <el-option label="上传用户" value="createByName" />
            </el-select>
          </el-form-item>
          <el-form-item prop="key" class="w-60">
            <el-input v-model="query.key" placeholder="请输入搜索关键词" clearable>
              <template #prefix>
                <icon icon="carbon:search" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="timeRange" label="上传时间" class="w-80">
            <el-date-picker
              v-model="query.timeRange" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit" :loading="tableRef?.loading">
              <template #icon>
                <icon icon="mdi:magnify" />
              </template>
              查询
            </el-button>
            <el-button native-type="reset">
              <template #icon>
                <icon icon="radix-icons:reset" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #left-actions>
        <el-button v-if="$hasPerm('sys:upfile:upload')" type="primary" @click="uploadDialogVisible = true">
          <template #icon>
            <icon icon="ep:upload" />
          </template>
          上传
        </el-button>
        <el-button-group v-if="$hasPerm('sys:upfile:deletePhysical')">
          <el-popconfirm title="确认要删除选中的项吗？" placement="bottom" @confirm="handleRemove(tableRef.selected)">
            <template #reference>
              <el-button title="删除" :disabled="!canAction">
                <template #icon>
                  <icon icon="ep:delete" />
                </template>
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </el-button-group>
      </template>
      <el-table-column type="selection" align="center" width="50" />
      <el-table-column prop="id" label="ID" align="center" width="100" />
      <el-table-column prop="fileName" label="文件名称" width="360" show-overflow-tooltip />
      <el-table-column prop="fileSize" label="文件大小" width="160" :formatter="formatFileSize" />
      <el-table-column prop="info" label="描述说明" show-overflow-tooltip />
      <el-table-column prop="createTime" label="上传时间" align="center" width="170" :formatter="formatElCellDateTime" />
      <el-table-column label="操作" align="center" width="200">
        <template #default="{ row }">
          <div class="space-x-2">
            <el-button v-if="$hasPerm('sys:upfile:view')" link @click="handlePreview(row)">
              预览
            </el-button>
            <el-button v-if="$hasPerm('sys:upfile:download')" link @click="handleDownload(row.id)">
              下载
            </el-button>
            <el-popconfirm v-if="$hasPerm('sys:upfile:deletePhysical')" title="确认要删除该文件吗？" placement="left" @confirm="handleRemove([row.id])">
              <template #reference>
                <el-button type="warning" link>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </table-list>
  </div>
  <el-dialog v-model="uploadDialogVisible" title="文件上传" width="43%" draggable :close-on-click-modal="false" :close-on-press-escape="false" @close="handleCanclelUpload">
    <ep-file-upload ref="uploadRef" :auto-upload="false" @submit-completed="uploadComplete" @upload-fail="uploadFail" />
    <template #footer>
      <el-button @click="uploadDialogVisible = false">
        取消
      </el-button>
      <el-button type="primary" :loading="uploading" @click="handleUpload">
        <template #icon>
          <icon icon="ep:upload" />
        </template>
        确定上传
      </el-button>
    </template>
  </el-dialog>
  <el-dialog
    v-model="previewDialogVisible" draggable destroy-on-close :width="previewMode === 'pdf' ? '90%' : ''"
    :title="previewMode !== 'image' ? '文件预览' : ''"
  >
    <!-- <iframe v-if="previewMode === 'pdf'" :src="previewUrl" frameborder="0" class="h-[90vh] w-full border-none" /> -->
    <div class="grid min-h-50 place-items-center">
      <img :src="previewUrl" alt="" class="max-w-full">
    </div>
  </el-dialog>
</template>
