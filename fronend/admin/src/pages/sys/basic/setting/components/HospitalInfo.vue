<script setup lang="ts">
import { uploadImage } from '~/api/common/sys'
import * as api from '~/api/sys/setting'
import { useFormReset, useLoading } from '~/composables'
import { tips } from '~/utils'

const formRef = useTemplateRef('formRef')
const { loading, withLoading } = useLoading()
const { saveData, resetData } = useFormReset(formRef)

const form = reactive({
  name: '',
  shortName: '',
  code: '',
  logo: '',
  phone: '',
  address: '',
})
const rules = reactive({
  name: [
    { required: true, message: '请输入医院名称' },
  ],
})

// 处理提交
async function handleSubmit() {
  await formRef.value.validate()
  await withLoading(async () => {
    await api.updateHospitalInfo(form)
    tips.success()
  })
}

// 处理重置
function handleReset() {
  resetData(form)
}

// logo上传
function handleUploadLogo(file: File) {
  return uploadImage(file, 'Logo图片', 500)
}

function handleLogoUploadSuccess(result: any) {
  if (result.data) {
    form.logo = result.data
  }
}

onMounted(async () => {
  const res = await api.getHospitalInfo()
  saveData(res.data)
  Object.assign(form, res.data)
})
</script>

<template>
  <el-config-provider>
    <el-form ref="formRef" :model="form" label-width="auto" :rules="rules" class="pl-2 pt-4" @submit.prevent="handleSubmit">
      <el-form-item prop="name" label="医院名称" class="w-120">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item prop="shortName" label="医院简称" class="w-80">
        <el-input v-model="form.shortName" />
      </el-form-item>
      <el-form-item prop="code" label="医院代码" class="w-80">
        <el-input v-model="form.code" />
      </el-form-item>
      <el-form-item prop="phone" label="电话" class="w-80">
        <el-input v-model="form.phone" />
      </el-form-item>
      <el-form-item prop="address" label="地址" class="w-120">
        <el-input v-model="form.address" />
      </el-form-item>
      <el-form-item prop="logo" label="医院Logo">
        <div class="line-height-[20px]">
          <div v-if="form.logo" class="mt-2">
            <el-image class="w-50" :src="form.logo" :preview-src-list="[form.logo]" hide-on-click-modal fit="cover" />
          </div>
          <file-upload accept="image/*" :max-size="2" :upload="handleUploadLogo" @success="handleLogoUploadSuccess">
            <template #default="{ triggerUpload, loading: uploadLoading }">
              <el-button :loading="uploadLoading" @click="triggerUpload">
                <template #icon>
                  <icon icon="material-symbols:upload-rounded" />
                </template>
                {{ form.logo ? '重新上传' : '上传' }}
              </el-button>
            </template>
          </file-upload>
        </div>
      </el-form-item>
      <el-form-item label=" " class="pt-4">
        <el-button type="primary" :loading="loading" native-type="submit" size="default">
          <template #icon>
            <icon icon="mdi:check" />
          </template>
          确定
        </el-button>
        <el-button size="default" @click="handleReset">
          <template #icon>
            <icon icon="radix-icons:reset" />
          </template>
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </el-config-provider>
</template>
