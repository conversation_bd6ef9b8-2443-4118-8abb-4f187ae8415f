<script setup lang="ts">
import type { FormRules } from 'element-plus'
import * as api from '~/api/sys/setting'
import { useFormReset, useLoading } from '~/composables'
import { tips } from '~/utils'

const formRef = useTemplateRef('formRef')
const { loading, withLoading } = useLoading()
const { saveData, resetData } = useFormReset(formRef)

const form = reactive({
  passwordTryMax: 5,
  passwordTryLock: 30,
  validCodeExpiry: 10,
  validCodeTryMax: 3,
})
const rules: FormRules = {
  passwordTryMax: [
    { required: true, message: '请输入密码尝试次数' },
    { type: 'number', min: 1, message: '密码尝试次数必须大于0' },
  ],
  passwordTryLock: [
    { required: true, message: '请输入密码错误超限锁定时间' },
    { type: 'number', min: 1, message: '密码错误超限锁定时间必须大于0' },
  ],
  validCodeExpiry: [
    { required: true, message: '请输入验证码有效时长' },
    { type: 'number', min: 1, message: '验证码有效时长必须大于0' },
  ],
  validCodeTryMax: [
    { required: true, message: '请输入验证码最大尝试错误次数' },
    { type: 'number', min: 1, message: '验证码最大尝试错误次数必须大于0' },
  ],
}

// 处理提交
async function handleSubmit() {
  await formRef.value.validate()
  await withLoading(async () => {
    await api.updateSafe(form)
    tips.success()
  })
}

// 处理重置
function handleReset() {
  resetData(form)
}

onMounted(async () => {
  const res = await api.getSafe()
  saveData(res.data)
  Object.assign(form, res.data)
})
</script>

<template>
  <el-config-provider>
    <el-form ref="formRef" label-width="auto" class="pt-4" :model="form" :rules="rules" @submit.prevent="handleSubmit">
      <el-form-item prop="passwordTryMax" label="密码尝试次数">
        <el-input-number v-model="form.passwordTryMax" step-strictly />
      </el-form-item>
      <el-form-item prop="passwordTryLock" label="密码错误超限锁定时间">
        <el-input-number v-model="form.passwordTryLock" step-strictly />
      </el-form-item>
      <el-form-item prop="validCodeExpiry" label="验证码有效时长">
        <el-input-number v-model="form.validCodeExpiry" step-strictly />
      </el-form-item>
      <el-form-item prop="validCodeTryMax" label="验证码最大尝试错误次数">
        <el-input-number v-model="form.validCodeTryMax" step-strictly />
      </el-form-item>
      <el-form-item label=" " class="pt-4">
        <el-button type="primary" :loading="loading" size="default" native-type="submit">
          <template #icon>
            <icon icon="mdi:check" />
          </template>
          确定
        </el-button>
        <el-button size="default" @click="handleReset">
          <template #icon>
            <icon icon="radix-icons:reset" />
          </template>
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </el-config-provider>
</template>
