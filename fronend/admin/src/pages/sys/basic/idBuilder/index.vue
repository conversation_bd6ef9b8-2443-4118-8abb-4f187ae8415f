<script setup lang="ts">
import * as api from '~/api/sys/idBuilder'
import { resetForm, tips } from '~/utils'

definePage({
  meta: {
    title: '编码配置',
    perm: 'sys:idBuilder',
    keepAlive: true,
    menu: true,
    order: 7,
  },
})

// 状态及数据
const queryFormRef = useTemplateRef('queryFormRef')
const tableRef = useTemplateRef<any>('tableRef')
const formRef = useTemplateRef('formRef')

const canAction = computed(() => tableRef.value?.hasSelected && !tableRef.value?.loading)

// 表单
const query = reactive({
  code: '',
})
const initData = {
  code: '',
  len: 6,
  startNo: 1,
  about: '',
}

// 验证规则
const rules = {
  code: [{ required: true, message: '请输入编码关键字' }],
}

// 操作
async function handleBy(ids: number[], fn: any) {
  if (ids && ids.length) {
    await tableRef.value.withLoading(async () => {
      await fn(ids)
      tips.success()
      await tableRef.value.fetchData()
    })
  }
}

// 删除
async function handleRemove(ids: number[]) {
  handleBy(ids, api.remove)
}
</script>

<template>
  <div class="p-4">
    <table-list ref="tableRef" :fetch-api="api.list" :query-params="query">
      <template #search>
        <el-form
          ref="queryFormRef" :model="query" :inline="true" @submit.prevent="tableRef.search()"
          @reset="resetForm(queryFormRef)"
        >
          <el-form-item prop="code" class="w-60">
            <el-input v-model="query.code" placeholder="请输入搜索关键字" clearable>
              <template #prefix>
                <icon icon="carbon:search" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit" :loading="tableRef?.loading">
              <template #icon>
                <icon icon="mdi:magnify" />
              </template>
              查询
            </el-button>
            <el-button native-type="reset">
              <template #icon>
                <icon icon="radix-icons:reset" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #left-actions>
        <el-button v-if="$hasPerm('sys:idBuilder:add')" type="primary" @click="formRef.add()">
          <template #icon>
            <icon icon="ep:plus" />
          </template>
          新增
        </el-button>
        <el-button-group class="ml-2">
          <el-popconfirm v-if="$hasPerm('sys:idBuilder:delete')" title="确认要删除选中的项吗？" placement="bottom" @confirm="handleRemove(tableRef.selected)">
            <template #reference>
              <el-button title="删除" :disabled="!canAction">
                <template #icon>
                  <icon icon="ep:delete" />
                </template>
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </el-button-group>
      </template>
      <el-table-column type="selection" align="center" width="50" />
      <el-table-column prop="id" label="ID" align="center" width="100" />
      <el-table-column prop="code" label="编码关键字" align="center" width="180" />
      <el-table-column prop="len" label="长度" align="center" width="120" />
      <el-table-column prop="startNo" label="起始编号" align="center" width="150" />
      <el-table-column prop="currentNo" label="当前编号" align="center" width="180" />
      <el-table-column prop="about" label="说明" show-overflow-tooltip />
      <el-table-column label="操作" align="center" width="200">
        <template #default="{ row }">
          <div class="space-x-2">
            <el-button v-if="$hasPerm('sys:idBuilder:edit')" link @click="formRef.edit(row.id)">
              编辑
            </el-button>
            <el-popconfirm
              v-if="$hasPerm('sys:idBuilder:delete')" title="确认要删除该项吗？" placement="left"
              @confirm="handleRemove([row.id])"
            >
              <template #reference>
                <el-button type="warning" link>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </table-list>
  </div>

  <!-- 编辑对话框 -->
  <detail-form
    ref="formRef" class="p-6" title="编码配置" :init-data="initData" :rules="rules" width="600px"
    :get-api="api.getEdit" :add-api="api.add" :edit-api="api.postEdit" @success="tableRef.fetchData()"
  >
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="编码关键字" prop="code">
          <el-input v-model="formRef.formData.code" :disabled="formRef.isEdit" placeholder="请输入编码关键字" maxlength="15" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="长度" prop="len">
          <el-input-number v-model="formRef.formData.len" :min="1" :max="20" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="起始编号" prop="startNo">
          <el-input-number v-model="formRef.formData.startNo" :min="1" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="说明" prop="about">
          <el-input v-model="formRef.formData.about" type="textarea" :rows="3" placeholder="请输入备注说明" maxlength="31" />
        </el-form-item>
      </el-col>
    </el-row>
  </detail-form>
</template>
