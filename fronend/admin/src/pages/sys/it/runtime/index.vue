<script setup lang="ts">
import { info as getRuntimeInfo } from '~/api/sys/runtime'
import { useLoading } from '~/composables'
import { formatBytesSize } from '~/utils'

definePage({
  meta: {
    title: '运行信息',
    perm: 'sys:runtime',
    menu: true,
    order: 1,
  },
})

interface DiskInfo {
  disk: string
  mount: string
  fsType: string
  total: number
  used: number
  free: number
  usage: number
}

// 运行信息
interface RuntimeInfo {
  osInfo: string
  pythonVersion: string
  processor: string
  machine: string
  cpuCount: number
  cpuLogicalCount: number
  cpuUsage: number
  totalMemory: number
  usedMemory: number
  processMemory: number
  processCpuUsage: number
  uptime: number
  serverTime: string
  appDir: string
  disks: DiskInfo[]
}

// 状态
const data = ref<RuntimeInfo>({} as RuntimeInfo)
const { loading, withLoading } = useLoading()

// 加载数据
async function fetchData() {
  await withLoading(async () => {
    const res = await getRuntimeInfo()
    data.value = res.data
  })
}

// 格式化时间
function formatDuration(seconds: number): string {
  if (!seconds)
    return ''
  const days = Math.floor(seconds / (24 * 3600))
  const hours = Math.floor((seconds % (24 * 3600)) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  const parts = []
  if (days > 0)
    parts.push(`${days}天`)
  if (hours > 0)
    parts.push(`${hours}小时`)
  if (minutes > 0)
    parts.push(`${minutes}分钟`)
  if (remainingSeconds > 0 || parts.length === 0)
    parts.push(`${remainingSeconds}秒`)

  return parts.join('')
}

// 计算内存使用率
function getMemoryUsagePercent(used: number, total: number) {
  return Math.round((used / total) * 100) || 0
}

// 刷新
async function handleRefresh() {
  try {
    await fetchData()
  }
  catch { }
}

onMounted(() => {
  fetchData()
})
</script>

<template>
  <div class="p-4">
    <el-row>
      <el-col :span="24" class="justify-end text-right">
        <el-button type="primary" round :loading="loading" @click="handleRefresh">
          <template #icon>
            <icon icon="mdi:refresh" />
          </template>
          刷新
        </el-button>
      </el-col>
    </el-row>
    <div v-loading="loading">
      <el-row :gutter="20" class="mt-5">
        <!-- 系统信息 -->
        <el-col :span="24" :sm="8" class="mb-4 sm:mb-0">
          <el-card shadow="hover" class="h-66">
            <template #header>
              <div class="flex items-center">
                <icon icon="mdi:server" size="5" class="mr-2 opacity-70" />
                <span>系统信息</span>
              </div>
            </template>
            <div class="p-2 space-y-5">
              <div class="flex justify-between">
                <span class="text-gray-500">操作系统</span>
                <span>{{ data.osInfo }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">服务器开机时间</span>
                <span>{{ formatDuration(data.uptime) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">服务器时间</span>
                <span>{{ data.serverTime }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">程序目录</span>
                <span>{{ data.appDir }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <!-- CPU信息 -->
        <el-col :span="24" :sm="8" class="mb-4 sm:mb-0">
          <el-card shadow="hover" class="h-66">
            <template #header>
              <div class="flex items-center">
                <icon icon="mdi:cpu-64-bit" size="6" class="mr-2 opacity-70" />
                <span>CPU信息</span>
              </div>
            </template>
            <div class="p-2 space-y-5">
              <div class="flex justify-between">
                <span class="text-gray-500">物理核心</span>
                <span>{{ data.cpuCount }} 核</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">逻辑核心</span>
                <span>{{ data.cpuLogicalCount }} 核</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">系统CPU使用率</span>
                <el-progress :percentage="data.cpuUsage" class="w-32" />
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">进程CPU使用率</span>
                <el-progress :percentage="data.processCpuUsage" class="w-32" />
              </div>
            </div>
          </el-card>
        </el-col>
        <!-- 内存信息 -->
        <el-col :span="24" :sm="8" class="mb-4 sm:mb-0">
          <el-card shadow="hover" class="h-66">
            <template #header>
              <div class="flex items-center">
                <icon icon="mdi:memory" size="6" class="mr-2 opacity-70" />
                <span>内存信息</span>
              </div>
            </template>
            <div class="p-2 space-y-5">
              <div class="flex justify-between">
                <span class="text-gray-500">总内存</span>
                <span>{{ formatBytesSize(data.totalMemory) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">已用内存</span>
                <el-progress :percentage="getMemoryUsagePercent(data.usedMemory, data.totalMemory)" class="w-32" />
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">可用内存</span>
                <span>{{ formatBytesSize(data.totalMemory - data.usedMemory) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">当前进程占用内存</span>
                <span>{{ formatBytesSize(data.processMemory) }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <!-- 磁盘信息区域 -->
      <el-row :gutter="20" class="mt-5">
        <el-col :span="24">
          <el-card shadow="never">
            <template #header>
              <div class="flex items-center">
                <icon icon="mdi:harddisk" size="6" class="mr-2 opacity-70" />
                <span>磁盘信息</span>
              </div>
            </template>
            <el-row :gutter="20">
              <el-col
                v-for="disk in data.disks" :key="disk.disk" :span="24" :sm="12" :md="8" :lg="6"
                class="border border-gray-100 border-solid transition-all duration-300 dark:border-gray-800 hover:border-gray-300 hover:shadow-md dark:hover:border-gray-600"
              >
                <div class="h-full p-4 pr-0 pt-1">
                  <div class="mb-3 flex items-center justify-between">
                    <h4 class="text-base font-medium">
                      {{ disk.mount }}
                    </h4>
                  </div>
                  <div class="mb-3 text-sm text-gray-500">
                    {{ disk.disk }} ({{ disk.fsType }})
                  </div>
                  <div class="space-y-3">
                    <el-progress
                      :percentage="disk.usage" :stroke-width="8"
                      :status="disk.usage >= 80 ? 'exception' : ''"
                    />
                    <div class="grid grid-cols-2 gap-2 text-sm">
                      <div class="text-gray-600">
                        <div class="text-gray-500">
                          总计
                        </div>
                        <div class="font-medium">
                          {{ formatBytesSize(disk.total) }}
                        </div>
                      </div>
                      <div class="text-gray-600">
                        <div class="text-gray-500">
                          使用率
                        </div>
                        <div class="font-medium">
                          {{ disk.usage?.toFixed(1) }}%
                        </div>
                      </div>
                      <div class="text-gray-600">
                        <div class="text-gray-500">
                          已用
                        </div>
                        <div class="font-medium">
                          {{ formatBytesSize(disk.used) }}
                        </div>
                      </div>
                      <div class="text-gray-600">
                        <div class="text-gray-500">
                          可用
                        </div>
                        <div class="font-medium">
                          {{ formatBytesSize(disk.free) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
