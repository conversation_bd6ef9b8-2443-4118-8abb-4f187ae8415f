<script setup lang="ts">
import * as api from '~/api/sys/job'
import { formatDate, formatDateShortTime, resetForm } from '~/utils'

const props = defineProps<{
  jobId?: string
}>()

// 状态及数据
const query = reactive({
  jobId: props.jobId || '',
  status: undefined as undefined | boolean,
  startTime: '',
  endTime: '',
  timeRange: undefined as any,
})
const formRef = useTemplateRef('formRef')
const tableRef = useTemplateRef<any>('tableRef')
const queryParams = computed(() => {
  return {
    ...query,
    timeRange: undefined,
  }
})

// 监听变更
watch(() => props.jobId, (newVal) => {
  query.jobId = newVal || ''
  nextTick(() => {
    tableRef.value.search()
  })
})
watch(() => query.timeRange, (newVal) => {
  if (newVal) {
    query.startTime = formatDate(newVal[0] as Date)
    query.endTime = formatDate(newVal[1] as Date)
  }
  else {
    query.startTime = ''
    query.endTime = ''
  }
})

// 格式化单元格日期
function formatCellDate(row: any, column: any, cellValue: any) {
  return formatDateShortTime(cellValue)
}

// 格式化耗时
function formatUseTime(row: any, column: any, cellValue: any) {
  return `${cellValue}ms`
}
</script>

<template>
  <table-list ref="tableRef" :fetch-api="api.log" :query-params="queryParams">
    <template #left-actions>
      <el-form
        ref="formRef" :model="query" :inline="true" @submit.prevent="tableRef.search()"
        @reset="resetForm(formRef)"
      >
        <el-form-item prop="jobId" label="任务标识" class="w-60">
          <el-input v-model="query.jobId" placeholder="请输入任务标识" clearable />
        </el-form-item>
        <el-form-item prop="status" label="状态" class="w-36">
          <el-select v-model="query.status" clearable>
            <el-option label="成功" :value="true" />
            <el-option label="失败" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item prop="timeRange" label="执行时间" class="w-80">
          <el-date-picker
            v-model="query.timeRange" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" native-type="submit" :loading="tableRef?.loading">
            <template #icon>
              <icon icon="mdi:magnify" />
            </template>
            查询
          </el-button>
          <el-button native-type="reset">
            <template #icon>
              <icon icon="radix-icons:reset" />
            </template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </template>
    <el-table-column prop="id" label="ID" align="center" width="100" />
    <el-table-column prop="jobId" label="任务标识" width="150" />
    <el-table-column prop="startTime" label="开始时间" align="center" width="200" :formatter="formatCellDate" />
    <el-table-column prop="endTime" label="结束时间" align="center" width="200" :formatter="formatCellDate" />
    <el-table-column prop="useTime" label="耗时" align="center" width="120" :formatter="formatUseTime" />
    <el-table-column prop="success" label="状态" align="center" width="130">
      <template #default="{ row }">
        <el-tag :type="row.success ? 'success' : 'danger'">
          {{ row.success ? '成功' : '失败' }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="msg" label="执行信息" show-overflow-tooltip />
  </table-list>
</template>
