<script setup lang="ts">
import Job from './components/Job.vue'
import Log from './components/Log.vue'

definePage({
  meta: {
    title: '定时任务',
    perm: 'sys:job',
    menu: true,
    order: 2,
  },
})

// 状态及数据
const activeTab = ref<'current' | 'log'>('current')
const jobId = ref<string>()

function handleShowLog(id: string) {
  jobId.value = id
  activeTab.value = 'log'
}
</script>

<template>
  <div class="p-4">
    <el-tabs v-model="activeTab">
      <el-tab-pane name="current" label="当前任务">
        <Job @show-log="handleShowLog" />
      </el-tab-pane>
      <el-tab-pane v-if="$hasPerm('sys:job:log')" name="log" label="运行日志" lazy>
        <Log :job-id="jobId" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
