<script setup lang="ts">
definePage({
  meta: {
    title: '开单登记',
    perm: 'TODO',
    menu: true,
    order: 1,
  },
})

// 左侧表单数据
const formModel = reactive({
  personnelType: '普通人员',
  checkupType: '健康体检',
  name: '123',
  gender: '女',
  birthDate: '1988-06-06',
  age: 26,
  phone: '12345678',
  idCard: '',
  maritalStatus: '已婚',
  pregnant: '否',
  occupation: '',
  education: '',
  region: '湖南省长沙市',
  nation: '汉族',
  entrust: false,
  darkCode: false,
  address: '',
  feeType: '自费',
  billingDoctor: '刘易飞',
  appointment: '',
  medicalCenter: '',
  notificationMethod: '短信',
  deliveryMethod: '自取或代领',
  email: '',
  uploadToExtranet: true,
  printReport: true,
  guideNurse: '',
  commonContacts: '',
  remarks: '',
  checkupRemarks: '',
})

// 右上角团体取号
const groupSearchForm = reactive({
  floor: '2层',
  useIdCard: true,
  useName: true,
  useGender: false,
  phone: '12345678',
  isGroup: false,
})

const groupList = ref([
  { archiveId: '2662066', checkupId: '2662066', name: '...', gender: '女', age: 23, phone: '13807318888' },
  { archiveId: '2913266', checkupId: '2913266', name: '...', gender: '女', age: 12, phone: '13807318888' },
  { archiveId: '5032970', checkupId: '5032970', name: '...', gender: '女', age: 23, phone: '13807318888' },
  { archiveId: '5039337', checkupId: '5039337', name: '...', gender: '女', age: 26, phone: '13807318888' },
])

// 左下角状态信息
const todayProjectList = ref([
  { name: '动脉硬化检测', remaining: 39, max: 90, opened: 51, vip: '', group: '' },
  { name: '眼底照相', remaining: 34, max: 60, opened: 26, vip: '', group: '' },
  { name: '口腔综合检查', remaining: 23, max: 25, opened: 2, vip: '', group: '' },
  { name: '肺功能+肺最大通气量', remaining: 12, max: 12, opened: 0, vip: '', group: '' },
])

// 底部费用信息
const feeForm = reactive({
  groupId: '',
  limit: 0,
  isVip: false,
  department: '',
  group: '',
  groupName: '',
  groupNotes: '',
  salesperson: '',
  customerService: '',
  checkupFee: 0,
  selfPay: 0,
  unifiedPay: 0,
  grouping: '',
})

const tabs = reactive({
  mainLeft: 'basic',
  bottom: 'project',
})

const historyModel = reactive({
  history: [],
  surgery: '无',
  surgery_desc: '',
  trauma: '无',
  trauma_desc: '',
  transfusion: '无',
  transfusion_desc: '',
  family: {},
  allergy: {
    drug: false,
    drug_desc: '',
    food: false,
    food_desc: '',
    other: false,
    other_desc: '',
  },
  habit: {
    smoke: '从不',
    smoke_day: '',
    smoke_year: '',
    drink: '从不',
    drink_day: '',
    drink_year: '',
    exercise: '从不',
    exercise_type: '',
    diet: '荤素均衡',
  },
  female: {
    menarche: '',
    cycle: '',
    menstrual: '',
    last_menstrual: '',
    childbirth: {
      full_term: '',
      premature: '',
      abortion: '',
      existing: '',
    },
  },
  occupational: '',
})

const blcjData = [{ id: 1, name: '肝癌术后' }, { id: 2, name: '视力下降' }, { id: 3, name: '便血' }, { id: 4, name: '瀵疡二指' }, { id: 5, name: '颈部不适史' }, { id: 6, name: '便血' }]

const questionnaireTreeData = [
  {
    id: 1,
    label: '全部',
    children: [
      {
        id: 11,
        label: '主观评估',
        children: [
          { id: 111, label: 'PHQ-9抑郁症筛查量表' },
          { id: 112, label: 'SAS焦虑自评量表' },
          { id: 113, label: '脑疲劳度测试问卷' },
        ],
      },
      {
        id: 12,
        label: '客观评估',
        children: [
          { id: 121, label: '客观评估子项1' },
        ],
      },
      {
        id: 13,
        label: '中医体质',
        children: [
          { id: 131, label: '中医体质子项1' },
        ],
      },
    ],
  },
]
const filterText = ref('')
const treeInst = ref()
watch(filterText, (val) => {
  treeInst.value!.filter(val)
})
function filterNode(value: string, data: any) {
  if (!value)
    return true
  return data.label.includes(value)
}

const questionnaireData = ref([
  { id: 1, question: '1. 您的精力好吗？', answer: null },
  { id: 2, question: '2. 您容易疲劳吗？', answer: null },
  { id: 3, question: '3. 您容易患感冒吗？', answer: null },
  { id: 4, question: '4. 您容易气喘吗？', answer: null },
  { id: 5, question: '5. 您说话声音低弱无力吗？', answer: null },
  { id: 6, question: '6. 您感到闷闷不乐、情绪低沉吗？', answer: null },
  { id: 7, question: '7. 您比一般人耐受不了寒冷吗？', answer: null },
  { id: 8, question: '8. 您比一般人耐受不了炎热吗？', answer: null },
  { id: 9, question: '9. 您容易紧张、心烦意乱吗？', answer: null },
  { id: 10, question: '10. 您面部或鼻部有油腻感或者油亮发光吗？', answer: null },
])

const answerOptions = ['没有', '很少', '有时', '经常', '总是']

function handleSelect(item: any, index: number) {
  if (item.answer === index)
    item.answer = null

  else
    item.answer = index
}

// 医生建议相关数据
const doctorAdviceData = ref([
  { id: 1, department: '内科', project: '血常规检查', gender: '不限', doctor: '', description: '' },
  { id: 2, department: '外科', project: '心电图检查', gender: '不限', doctor: '', description: '' },
  { id: 3, department: '妇科', project: '妇科常规检查', gender: '女', doctor: '', description: '' },
  { id: 4, department: '眼科', project: '视力检查', gender: '不限', doctor: '', description: '' },
  { id: 5, department: '耳鼻喉科', project: '听力检查', gender: '不限', doctor: '', description: '' },
  { id: 6, department: '口腔科', project: '口腔检查', gender: '不限', doctor: '', description: '' },
  { id: 7, department: '皮肤科', project: '皮肤检查', gender: '不限', doctor: '', description: '' },
  { id: 8, department: '骨科', project: 'X光检查', gender: '不限', doctor: '', description: '' },
])

const selectedAdviceItems = ref([])

// 表格选择状态
const selectedWaitingItems = ref([])
const selectedAdvicedItems = ref([])

// 编辑说明的状态
const editingDescription = ref(null)
const tempDescription = ref('')

function startEditDescription(item) {
  editingDescription.value = item.id
  tempDescription.value = item.description || ''
  // 使用 nextTick 确保输入框渲染后自动聚焦
  nextTick(() => {
    const input = document.querySelector(`[data-edit-id="${item.id}"] input`)
    if (input) {
      input.focus()
      input.select()
    }
  })
}

function saveDescription(item) {
  item.description = tempDescription.value.trim()
  editingDescription.value = null
  tempDescription.value = ''
}

function cancelEditDescription() {
  editingDescription.value = null
  tempDescription.value = ''
}

// 添加建议项目
function addAdviceItem(item) {
  if (!selectedAdviceItems.value.find(selected => selected.id === item.id)) {
    const newItem = { ...item, doctor: '当前医师', description: '' }
    selectedAdviceItems.value.push(newItem)
  }
}

// 移除建议项目
function removeAdviceItem(item) {
  const index = selectedAdviceItems.value.findIndex(selected => selected.id === item.id)
  if (index > -1) {
    selectedAdviceItems.value.splice(index, 1)
  }
}

// 上移项目
function moveUp(index) {
  if (index > 0) {
    const temp = selectedAdviceItems.value[index]
    selectedAdviceItems.value[index] = selectedAdviceItems.value[index - 1]
    selectedAdviceItems.value[index - 1] = temp
  }
}

// 下移项目
function moveDown(index) {
  if (index < selectedAdviceItems.value.length - 1) {
    const temp = selectedAdviceItems.value[index]
    selectedAdviceItems.value[index] = selectedAdviceItems.value[index + 1]
    selectedAdviceItems.value[index + 1] = temp
  }
}

// 批量操作方法
function addSelectedItems() {
  selectedWaitingItems.value.forEach(item => {
    addAdviceItem(item)
  })
  selectedWaitingItems.value = []
}

function removeSelectedItems() {
  selectedAdvicedItems.value.forEach(item => {
    removeAdviceItem(item)
  })
  selectedAdvicedItems.value = []
}

function moveSelectedUp() {
  if (selectedAdvicedItems.value.length === 1) {
    const item = selectedAdvicedItems.value[0]
    const index = selectedAdviceItems.value.findIndex(selected => selected.id === item.id)
    moveUp(index)
  }
}

function moveSelectedDown() {
  if (selectedAdvicedItems.value.length === 1) {
    const item = selectedAdvicedItems.value[0]
    const index = selectedAdviceItems.value.findIndex(selected => selected.id === item.id)
    moveDown(index)
  }
}

// 表格选择事件处理
function handleWaitingSelectionChange(selection) {
  selectedWaitingItems.value = selection
}

function handleAdvicedSelectionChange(selection) {
  selectedAdvicedItems.value = selection
}
</script>

<template>
  <div class="h-content-box flex flex-col bg-gray-100 dark:bg-dark">
    <!-- 顶部操作栏 -->
    <div
      class="flex items-center justify-between border-b border-gray-200 bg-white p-3 dark:border-dark-100 dark:bg-dark-200"
    >
      <el-form :inline="true" class="flex-1">
        <el-form-item label="查号" class="mb-0 mr-1!">
          <el-input placeholder="请输入" />
        </el-form-item>
        <el-form-item class="mb-0">
          <el-button type="primary" plain>
            <template #icon>
              <icon icon="mdi:magnify" />
            </template>
            查询
          </el-button>
        </el-form-item>
        <el-form-item class="mb-0 mr-2!">
          <el-button>读身份证</el-button>
        </el-form-item>
        <el-form-item class="mb-0 mr-2!">
          <el-button>读医保卡</el-button>
        </el-form-item>
        <el-form-item class="mb-0">
          <el-button>卡查询</el-button>
        </el-form-item>
        <el-form-item class="mb-0">
          <el-button>预约</el-button>
        </el-form-item>
        <el-divider direction="vertical" class="mr-6!" />
        <el-form-item label="" class="mb-0">
          <el-checkbox label="团体取号" />
        </el-form-item>
        <el-form-item class="mb-0 mr-2!">
          <el-button type="primary">
            取号
          </el-button>
        </el-form-item>
        <el-form-item class="mb-0">
          <el-button plain>
            清除
          </el-button>
        </el-form-item>
      </el-form>
      <div class="flex items-center">
        <div class="flex items-center gap-2">
          <el-button plain>
            取消登记
          </el-button>
          <el-button type="success">
            <template #icon>
              <icon icon="ep:check" />
            </template>
            完成登记
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主体内容区 -->
    <div class="flex-1">
      <el-splitter layout="vertical">
        <el-splitter-panel>
          <div class="relative box-border h-full p-2 pb-1">
            <div class="absolute right-4 top-2 z-1">
              <el-text class="mx-1 line-height-[40px]" type="primary">
                正在登记：刘亦非 女 26岁
              </el-text>
            </div>
            <el-tabs v-model="tabs.mainLeft" type="border-card" class="content-p-0 h-full rounded">
              <el-tab-pane label="基本资料" name="basic" class="h-full">
                <el-splitter>
                  <el-splitter-panel collapsible>
                    <el-scrollbar>
                      <div class="flex flex-col p-5 pt-0">
                        <div
                          class="h-16 flex items-center justify-between border-b-1 border-b-gray-200 border-b-solid dark:border-b-gray-600"
                        >
                          <div>
                            <el-button type="primary" plain>
                              新增
                            </el-button>
                            <el-button type="success" plain class="mr-5">
                              保存
                            </el-button>
                          </div>
                          <div>
                            <el-button plain>
                              删除
                            </el-button>
                            <el-button plain>
                              修改
                            </el-button>
                            <el-button plain class="mr-5">
                              取消
                            </el-button>
                            <el-button plain>
                              快捷
                            </el-button>
                          </div>
                        </div>
                        <div class="flex-1 pt-5">
                          <div class="grid grid-cols-12 gap-2">
                            <!-- 表单内容 -->
                            <div class="col-span-9">
                              <el-form :model="formModel" label-width="70px">
                                <div class="grid grid-cols-12 gap-x-3">
                                  <el-form-item label="体检号" class="col-span-6">
                                    <el-input v-model="formModel.name" disabled />
                                  </el-form-item>
                                  <el-form-item label="档案号" class="col-span-6">
                                    <el-input v-model="formModel.name" />
                                  </el-form-item>
                                  <el-form-item label="人员类型" class="col-span-6">
                                    <el-select v-model="formModel.personnelType" />
                                  </el-form-item>
                                  <el-form-item label="体检类型" class="col-span-6">
                                    <el-select v-model="formModel.checkupType" />
                                  </el-form-item>
                                  <el-form-item label="姓名" class="col-span-6">
                                    <el-input v-model="formModel.name" />
                                  </el-form-item>
                                  <el-form-item label="电话" class="col-span-6">
                                    <el-input v-model="formModel.phone" />
                                  </el-form-item>
                                  <el-form-item label="身份证" class="col-span-12">
                                    <el-input v-model="formModel.idCard" />
                                  </el-form-item>
                                  <el-form-item label="生日" class="hide-input-icon col-span-4">
                                    <el-date-picker v-model="formModel.birthDate" type="date" />
                                  </el-form-item>
                                  <el-form-item label="年龄" class="col-span-4">
                                    <el-input v-model="formModel.age" />
                                  </el-form-item>
                                  <el-form-item label="民族" class="col-span-4">
                                    <el-input v-model="formModel.nation" />
                                  </el-form-item>
                                  <el-form-item label="性别" class="col-span-4">
                                    <el-select v-model="formModel.gender">
                                      <el-option label="男" value="男" />
                                      <el-option label="女" value="女" />
                                    </el-select>
                                  </el-form-item>
                                  <el-form-item label="婚姻" class="col-span-4">
                                    <el-select v-model="formModel.maritalStatus" />
                                  </el-form-item>
                                  <el-form-item label="孕否" class="col-span-4">
                                    <el-select v-model="formModel.pregnant" />
                                  </el-form-item>
                                  <el-form-item label="职业" class="col-span-8">
                                    <el-input v-model="formModel.occupation" />
                                  </el-form-item>
                                  <el-form-item label="教育" class="col-span-4">
                                    <el-select>
                                      <el-option label="本科" value="本科" />
                                      <el-option label="专科" value="专科" />
                                    </el-select>
                                  </el-form-item>
                                  <el-form-item label="地区" class="col-span-5">
                                    <el-input v-model="formModel.region" />
                                  </el-form-item>
                                  <el-form-item label="住址" class="col-span-7">
                                    <el-input v-model="formModel.address" />
                                  </el-form-item>
                                  <el-divider class="col-span-12" />
                                  <el-form-item label="费别" class="col-span-6">
                                    <el-select v-model="formModel.feeType" />
                                  </el-form-item>
                                  <el-form-item label="开单医师" class="col-span-6">
                                    <el-input v-model="formModel.billingDoctor" />
                                  </el-form-item>
                                  <el-form-item label="预约" class="col-span-6">
                                    <el-input v-model="formModel.appointment" />
                                  </el-form-item>
                                  <el-form-item label="医保中心" class="col-span-6">
                                    <el-input v-model="formModel.medicalCenter" />
                                  </el-form-item>
                                  <el-divider class="col-span-12" />
                                  <el-form-item label="通知方式" class="col-span-6">
                                    <el-select v-model="formModel.notificationMethod" />
                                  </el-form-item>
                                  <el-form-item label="领取方式" class="col-span-6">
                                    <el-select v-model="formModel.deliveryMethod" />
                                  </el-form-item>
                                  <el-form-item label="电子邮件" class="col-span-12">
                                    <div class="w-full flex items-center gap-4">
                                      <el-input v-model="formModel.email" />
                                      <el-checkbox v-model="formModel.uploadToExtranet" class="ml-8">
                                        上传外网
                                      </el-checkbox>
                                      <el-checkbox v-model="formModel.printReport">
                                        打印报告
                                      </el-checkbox>
                                    </div>
                                  </el-form-item>
                                  <el-form-item label="导诊护士" class="col-span-6">
                                    <el-input v-model="formModel.guideNurse" />
                                  </el-form-item>
                                  <el-form-item label="常用联系" class="col-span-6">
                                    <el-input v-model="formModel.commonContacts" />
                                  </el-form-item>
                                  <el-form-item label="备注" class="col-span-12">
                                    <el-input v-model="formModel.remarks" type="textarea" :rows="2" />
                                  </el-form-item>
                                  <el-form-item label="" class="col-span-12">
                                    <template #label>
                                      <div class="pt-2 line-height-tight">
                                        检验采集<br>备注
                                      </div>
                                    </template>
                                    <el-input v-model="formModel.checkupRemarks" type="textarea" :rows="2" />
                                  </el-form-item>
                                </div>
                              </el-form>
                            </div>
                            <!-- 照片区 -->
                            <div class="col-span-3 flex flex-col items-center justify-start">
                              <div
                                class="h-48 w-40 flex flex-col items-center justify-center border-2 border-color-gray rounded-md border-dashed bg-gray-50 dark:border-color-gray-600 dark:bg-dark-300"
                              >
                                <div class="text-xl text-gray-400">
                                  暂无
                                </div>
                              </div>
                              <div class="mt-4">
                                <el-button plain type="primary" class="w-full">
                                  拍照
                                </el-button>
                                <div class="mt-2">
                                  <el-button>
                                    弃拍
                                  </el-button>
                                  <el-button>
                                    补拍
                                  </el-button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-scrollbar>
                  </el-splitter-panel>
                  <el-splitter-panel collapsible>
                    <el-scrollbar>
                      <div class="flex flex-col">
                        <div class="flex flex-col">
                          <div class="h-16 flex items-center justify-between border-b-1 border-b-gray-200 border-b-solid px-5 py-1 dark:border-b-gray-600">
                            <el-form inline class="flex flex-1 items-center">
                              <div class="flex flex-col">
                                <el-checkbox label="身份证" class="mb-1 flex-1" size="small" />
                                <el-checkbox label="姓名" class="mb-1 flex-1" size="small" />
                                <el-checkbox label="性别" class="flex-1" size="small" />
                              </div>
                              <div class="flex flex-col">
                                <el-form-item label="电话" class="mb-1" size="small">
                                  <el-input v-model="groupSearchForm.phone" clearable class="w-30" />
                                </el-form-item>
                                <el-form-item label="团体" class="mb-0" size="small">
                                  <el-input v-model="groupSearchForm.phone" clearable class="w-30" />
                                </el-form-item>
                              </div>
                              <el-button plain type="primary" class="h-48px">
                                查档
                              </el-button>
                            </el-form>
                            <div>
                              <el-button>
                                档案取回
                              </el-button>
                              <el-button type="primary">
                                首次建档
                              </el-button>
                            </div>
                          </div>
                          <div class="mx-1 flex-1 pt-2">
                            <el-table :data="groupList" stripe highlight-current-row>
                              <el-table-column type="index" label="#" align="center" width="50" />
                              <el-table-column prop="archiveId" label="档案号" align="center" width="110" show-overflow-tooltip />
                              <el-table-column prop="checkupId" label="体检号" align="center" width="110" show-overflow-tooltip />
                              <el-table-column prop="name" label="姓名" align="center" show-overflow-tooltip />
                              <el-table-column prop="gender" label="性别" align="center" width="80" />
                              <el-table-column prop="age" label="年龄" align="center" width="80" />
                              <el-table-column prop="phone" label="电话" align="center" show-overflow-tooltip />
                              <el-table-column prop="phone" label="更多" align="center" width="110">
                                <template #default="{ row }">
                                  <el-button link>
                                    查看
                                  </el-button>
                                  <el-button link>
                                    照片
                                  </el-button>
                                </template>
                              </el-table-column>
                            </el-table>
                          </div>
                        </div>
                      </div>
                    </el-scrollbar>
                  </el-splitter-panel>
                </el-splitter>
              </el-tab-pane>
              <el-tab-pane label="病史采集" name="history" class="h-full">
                <el-splitter>
                  <el-splitter-panel>
                    <el-scrollbar>
                      <el-tabs class="tabs-bscj box-border p-5">
                        <el-tab-pane label="既往史">
                          <div class="mb-1">
                            <el-input placeholder="输入拼音过滤" class="w-60" clearable>
                              <template #prefix>
                                <icon icon="mdi:magnify" />
                              </template>
                            </el-input>
                          </div>
                          <grid-panl :highlights="[blcjData[0], blcjData[1]]" :data="blcjData" @item-click="(item) => console.log(item)" />
                        </el-tab-pane>
                        <el-tab-pane label="手术史">
                          <div class="mb-1">
                            <el-input placeholder="输入拼音过滤" class="w-60" clearable>
                              <template #prefix>
                                <icon icon="mdi:magnify" />
                              </template>
                            </el-input>
                          </div>
                          <grid-panl :highlights="[blcjData[0], blcjData[1]]" :data="blcjData" @item-click="(item) => console.log(item)" />
                        </el-tab-pane>
                        <el-tab-pane label="体检史">
                          <div class="mb-1">
                            <el-input placeholder="输入拼音过滤" class="w-60" clearable>
                              <template #prefix>
                                <icon icon="mdi:magnify" />
                              </template>
                            </el-input>
                          </div>
                          <grid-panl :highlights="[blcjData[0], blcjData[1]]" :data="blcjData" @item-click="(item) => console.log(item)" />
                        </el-tab-pane>
                        <el-tab-pane label="家庭史">
                          <div class="mb-1">
                            <el-input placeholder="输入拼音过滤" class="w-60" clearable>
                              <template #prefix>
                                <icon icon="mdi:magnify" />
                              </template>
                            </el-input>
                          </div>
                          <grid-panl :highlights="[blcjData[0], blcjData[1]]" :data="blcjData" @item-click="(item) => console.log(item)" />
                        </el-tab-pane>
                        <el-tab-pane label="月经史">
                          <div class="mb-1">
                            <el-input placeholder="输入拼音过滤" class="w-60" clearable>
                              <template #prefix>
                                <icon icon="mdi:magnify" />
                              </template>
                            </el-input>
                          </div>
                          <grid-panl :highlights="[blcjData[0], blcjData[1]]" :data="blcjData" @item-click="(item) => console.log(item)" />
                        </el-tab-pane>
                        <el-tab-pane label="现病史">
                          <div class="mb-1">
                            <el-input placeholder="输入拼音过滤" class="w-60" clearable>
                              <template #prefix>
                                <icon icon="mdi:magnify" />
                              </template>
                            </el-input>
                          </div>
                          <grid-panl :highlights="[blcjData[0], blcjData[1]]" :data="blcjData" @item-click="(item) => console.log(item)" />
                        </el-tab-pane>
                        <el-tab-pane label="全部">
                          <div class="mb-1">
                            <el-input placeholder="输入拼音过滤" class="w-60" clearable>
                              <template #prefix>
                                <icon icon="mdi:magnify" />
                              </template>
                            </el-input>
                          </div>
                          <grid-panl :highlights="[blcjData[0], blcjData[1]]" :data="blcjData" @item-click="(item) => console.log(item)" />
                        </el-tab-pane>
                      </el-tabs>
                    </el-scrollbar>
                  </el-splitter-panel>
                  <el-splitter-panel size="30%">
                    <div class="mx-1 h-full flex flex-col">
                      <div class="h-[calc(100%-160px)]">
                        <el-scrollbar>
                          <div class="pt-4">
                            <el-button-group class="mb-2 ml-2">
                              <el-button>
                                <template #icon>
                                  <icon icon="ep:delete" />
                                </template>
                                删除选中
                              </el-button>
                            </el-button-group>
                            <el-table :data="todayProjectList" stripe height="100%">
                              <el-table-column type="selection" align="center" width="50" />
                              <el-table-column type="index" align="center" label="#" width="50" />
                              <el-table-column prop="name" label="类型" width="120" />
                              <el-table-column prop="name" label="病史名称" show-overflow-tooltip />
                              <el-table-column label="删除" align="center" width="80">
                                <template #default="{ row }">
                                  <el-button link>
                                    删除
                                  </el-button>
                                </template>
                              </el-table-column>
                            </el-table>
                          </div>
                        </el-scrollbar>
                      </div>
                      <div class="h-[160px]">
                        <el-divider content-position="left">
                          新增病史
                        </el-divider>
                        <el-form class="px-6">
                          <el-form-item label="病史名称">
                            <el-input />
                          </el-form-item>
                          <el-form-item label="病史分类">
                            <div class="w-full flex">
                              <el-select class="flex-1">
                                <el-option label="病史分类1" value="1" />
                                <el-option label="病史分类2" value="2" />
                                <el-option label="病史分类3" value="3" />
                              </el-select>
                              <el-button type="primary" plain class="ml-2">
                                新增病史
                              </el-button>
                            </div>
                          </el-form-item>
                        </el-form>
                      </div>
                    </div>
                  </el-splitter-panel>
                </el-splitter>
              </el-tab-pane>
              <el-tab-pane label="问卷量表" name="quest" class="h-full">
                <el-splitter>
                  <el-splitter-panel size="291px">
                    <div class="h-full flex flex-col">
                      <div class="p-3">
                        <el-input placeholder="输入拼音过滤" clearable>
                          <template #prefix>
                            <icon icon="mdi:magnify" />
                          </template>
                        </el-input>
                      </div>
                      <el-scrollbar class="flex-1">
                        <el-tree
                          :data="[
                            {
                              id: 1,
                              label: '问卷调查',
                              children: [
                                {
                                  id: 4,
                                  label: '开单量表',
                                  children: [
                                    {
                                      id: 5,
                                      label: '工作情况',
                                    },
                                    {
                                      id: 5,
                                      label: '饮食情况',
                                    },
                                    {
                                      id: 5,
                                      label: '休闲娱乐情况',
                                    },
                                  ],
                                },
                                {
                                  id: 4,
                                  label: '脑卒中采集表',
                                  children: [
                                    {
                                      id: 5,
                                      label: '脑卒中研究对象体质',
                                    },
                                    {
                                      id: 5,
                                      label: '脑卒中研究对象采集',
                                    },
                                  ],
                                },
                              ],
                            },
                            {
                              id: 2,
                              label: '食物频率',
                              children: [
                                {
                                  id: 5,
                                  label: '早中晚餐食物频率',
                                },
                                {
                                  id: 6,
                                  label: '饮食偏好',
                                },
                              ],
                            },
                          ]"
                          default-expand-all
                          :filter-node-method="filterNode"
                        />
                      </el-scrollbar>
                      <div class="p-3">
                        <el-button type="primary" plain size="" class="w-full">
                          问卷结果
                        </el-button>
                      </div>
                    </div>
                  </el-splitter-panel>
                  <el-splitter-panel>
                    <el-table
                      stripe border :show-header="false"
                      class="h-full w-full"
                      :row-style="{ height: '100px' }"
                      :cell-style="(item) => {
                        return item.columnIndex === 0 ? { color: 'red' } : {}
                      }"
                      :data=" [
                        {
                          name: '您平常口味品如何？(可多选)',
                          state: '无特殊',
                          city: '偏淡',
                          address: '偏咸',
                          zip: '偏油',
                        },
                        {
                          name: '这里到是抽个组件，再做细节调整，比如居中，多选，高亮，问题列样式区分',
                          state: '无特殊',
                          city: '偏淡',
                          address: '偏咸',
                          zip: '偏油',
                        },
                      ]"
                    >
                      <el-table-column fixed prop="name" label="问题" width="300" />
                      <el-table-column prop="state" label="State" width="150" />
                      <el-table-column prop="city" label="City" width="150" />
                      <el-table-column prop="address" label="Address" width="150" />
                      <el-table-column prop="zip" label="Zip" width="150" />
                    </el-table>
                  </el-splitter-panel>
                </el-splitter>
              </el-tab-pane>
              <el-tab-pane label="项目选定" name="project" class="h-full">
                <el-scrollbar class="h-full">
                  项目选定区域内容
                </el-scrollbar>
              </el-tab-pane>
              <el-tab-pane label="医生建议" name="advice" class="h-full">
                <div class="h-full w-full flex bg-white dark:bg-dark-200">
                  <!-- 左侧：待建议项目 -->
                  <div class="flex-1 flex flex-col border-r border-gray-200 dark:border-gray-600">
                    <div class="h-12 flex items-center justify-between border-b border-gray-200 bg-gray-50 px-4 font-medium dark:border-gray-600 dark:bg-gray-800">
                      <span>待建议项目</span>
                      <el-text type="info" size="small">双击行或点击加入按钮添加项目</el-text>
                    </div>
                    <div class="flex-1 overflow-hidden">
                      <el-table
                        :data="doctorAdviceData"
                        height="100%"
                        stripe
                        highlight-current-row
                        @row-dblclick="addAdviceItem"
                        @selection-change="handleWaitingSelectionChange"
                      >
                        <el-table-column type="selection" width="50" align="center" />
                        <el-table-column type="index" label="#" width="50" align="center" />
                        <el-table-column prop="department" label="科室" width="100" align="center" />
                        <el-table-column prop="project" label="申请项目" min-width="120" show-overflow-tooltip />
                        <el-table-column prop="gender" label="项目性别" width="80" align="center" />
                        <el-table-column label="操作" width="80" align="center">
                          <template #default="{ row }">
                            <el-button
                              type="primary"
                              link
                              size="small"
                              @click="addAdviceItem(row)"
                            >
                              加入
                            </el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </div>

                  <!-- 中间：操作按钮 -->
                  <div class="w-20 flex flex-col items-center justify-center gap-4 bg-gray-50 dark:bg-gray-800">
                    <el-tooltip content="加入选中项目" placement="left">
                      <el-button
                        type="primary"
                        circle
                        size="small"
                        @click="addSelectedItems"
                      >
                        <icon icon="ep:right" />
                      </el-button>
                    </el-tooltip>
                    <el-tooltip content="移除选中项目" placement="left">
                      <el-button
                        type="danger"
                        circle
                        size="small"
                        @click="removeSelectedItems"
                      >
                        <icon icon="ep:left" />
                      </el-button>
                    </el-tooltip>
                    <el-divider class="my-2!" />
                    <el-tooltip content="上移选中项目" placement="left">
                      <el-button
                        type="info"
                        circle
                        size="small"
                        @click="moveSelectedUp"
                      >
                        <icon icon="ep:top" />
                      </el-button>
                    </el-tooltip>
                    <el-tooltip content="下移选中项目" placement="left">
                      <el-button
                        type="info"
                        circle
                        size="small"
                        @click="moveSelectedDown"
                      >
                        <icon icon="ep:bottom" />
                      </el-button>
                    </el-tooltip>
                  </div>

                  <!-- 右侧：已建议项目 -->
                  <div class="flex-1 flex flex-col">
                    <div class="h-12 flex items-center justify-between border-b border-gray-200 bg-gray-50 px-4 font-medium dark:border-gray-600 dark:bg-gray-800">
                      <span>已建议项目</span>
                      <el-text type="info" size="small">点击说明列可编辑内容</el-text>
                    </div>
                    <div class="flex-1 overflow-hidden">
                      <el-table
                        :data="selectedAdviceItems"
                        height="100%"
                        stripe
                        highlight-current-row
                        @selection-change="handleAdvicedSelectionChange"
                      >
                        <el-table-column type="selection" width="50" align="center" />
                        <el-table-column type="index" label="#" width="50" align="center" />
                        <el-table-column prop="project" label="申请项目" min-width="120" show-overflow-tooltip />
                        <el-table-column prop="doctor" label="医师" width="100" align="center" />
                        <el-table-column label="说明" min-width="150">
                          <template #default="{ row }">
                            <div v-if="editingDescription === row.id" class="flex items-center gap-2" :data-edit-id="row.id">
                              <el-input
                                v-model="tempDescription"
                                size="small"
                                placeholder="请输入说明"
                                @keyup.enter="saveDescription(row)"
                                @keyup.esc="cancelEditDescription"
                                @blur="saveDescription(row)"
                              />
                              <el-button
                                type="primary"
                                size="small"
                                @click="saveDescription(row)"
                              >
                                <icon icon="ep:check" />
                              </el-button>
                              <el-button
                                size="small"
                                @click="cancelEditDescription"
                              >
                                <icon icon="ep:close" />
                              </el-button>
                            </div>
                            <div
                              v-else
                              class="cursor-pointer hover:bg-gray-100 min-h-6 p-1 rounded transition-colors dark:hover:bg-gray-700"
                              @click="startEditDescription(row)"
                            >
                              <el-text v-if="row.description" class="text-sm">
                                {{ row.description }}
                              </el-text>
                              <el-text v-else type="info" class="text-sm">
                                点击添加说明
                              </el-text>
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column prop="department" label="科室" width="100" align="center" />
                        <el-table-column label="操作" width="120" align="center">
                          <template #default="{ row, $index }">
                            <el-button
                              type="info"
                              link
                              size="small"
                              :disabled="$index === 0"
                              @click="moveUp($index)"
                            >
                              上移
                            </el-button>
                            <el-button
                              type="info"
                              link
                              size="small"
                              :disabled="$index === selectedAdviceItems.length - 1"
                              @click="moveDown($index)"
                            >
                              下移
                            </el-button>
                            <el-button
                              type="danger"
                              link
                              size="small"
                              @click="removeAdviceItem(row)"
                            >
                              移除
                            </el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-splitter-panel>
        <!-- 底部面板 -->
        <el-splitter-panel collapsible size="220px">
          <div class="box-border h-full p-2 pt-1">
            <el-card shadow="never" :body-style="{ padding: 0, height: '100%' }" class="h-full">
              <div class="h-full flex">
                <div class="h-full w-8 flex select-none items-center justify-center border-r-1 border-r-gray-200 border-r-solid" style="writing-mode: vertical-rl; letter-spacing: 5px;">
                  <el-text class="mx-1">
                    状态信息栏
                  </el-text>
                </div>
                <el-splitter class="flex-1">
                  <el-splitter-panel collapsible>
                    <el-scrollbar>
                      <el-tabs v-model="tabs.bottom" class="h-full flex flex-col px-2">
                        <el-tab-pane label="今日项目开单量" name="project">
                          <el-form class="flex items-center p-2">
                            <el-form-item label="条件" class="mb-0 mr-2" size="small">
                              <el-input />
                            </el-form-item>
                            <el-form-item label="贵宾量" class="mb-0 mr-2 w-30" size="small">
                              <el-input disabled />
                            </el-form-item>
                            <el-button size="small">
                              <template #icon>
                                <icon icon="ep:refresh" />
                              </template>
                              刷新
                            </el-button>
                          </el-form>
                          <el-table :data="todayProjectList" stripe size="small" height="100%" :header-cell-style="{ backgroundColor: 'var(--el-fill-color-lighter)', color: 'var(--el-text-color-regular)' }">
                            <el-table-column type="index" label="#" width="30" />
                            <el-table-column prop="name" label="申请项目名称" show-overflow-tooltip />
                            <el-table-column prop="remaining" label="剩余量" align="center" width="70" />
                            <el-table-column prop="max" label="最大量" align="center" width="70" />
                            <el-table-column prop="opened" label="已开量" align="center" width="70" />
                            <el-table-column prop="vip" label="贵宾" align="center" width="60">
                              <el-checkbox />
                            </el-table-column>
                            <el-table-column prop="group" label="团体" align="center" width="60">
                              <el-checkbox />
                            </el-table-column>
                          </el-table>
                        </el-tab-pane>
                        <el-tab-pane label="排队取号量" name="queue">
                          排队取号量
                        </el-tab-pane>
                      </el-tabs>
                    </el-scrollbar>
                  </el-splitter-panel>
                  <el-splitter-panel collapsible>
                    <el-scrollbar>
                      <div class="h-full flex flex-col">
                        <div class="flex items-center justify-center border-b-1 border-b-gray-200 border-b-solid py-1">
                          <el-text>团体信息</el-text>
                        </div>
                        <div class="flex-1 p-3">
                          <el-form :model="feeForm" label-width="60px" class="grid grid-cols-12 gap-x-2 p-2" size="small">
                            <el-form-item label="团体ID" class="col-span-7 mb-1!">
                              <el-input v-model="feeForm.groupId" />
                            </el-form-item>
                            <el-form-item label="限额" class="col-span-5 mb-1!">
                              <el-input v-model="feeForm.limit" />
                            </el-form-item>
                            <el-form-item label="VIP" class="col-span-3 mb-1!">
                              <el-checkbox />
                            </el-form-item>
                            <el-form-item label="科室" class="col-span-4 mb-1!">
                              <el-input v-model="feeForm.department" />
                            </el-form-item>
                            <el-form-item label="分组" class="col-span-5 mb-1!">
                              <el-input v-model="feeForm.grouping" />
                            </el-form-item>
                            <el-form-item label="团体名称" class="col-span-12 mb-1!">
                              <el-input v-model="feeForm.groupName" />
                            </el-form-item>
                            <el-form-item label="团体备注" class="col-span-12 mb-1!">
                              <el-input v-model="feeForm.groupNotes" />
                            </el-form-item>
                            <el-form-item label="销售员" class="col-span-6 mb-1!">
                              <el-input v-model="feeForm.salesperson" />
                            </el-form-item>
                            <el-form-item label="客服员" class="col-span-6 mb-1!">
                              <el-input v-model="feeForm.customerService" />
                            </el-form-item>
                          </el-form>
                        </div>
                      </div>
                    </el-scrollbar>
                  </el-splitter-panel>
                  <el-splitter-panel collapsible>
                    <el-scrollbar>
                      <div class="h-full flex flex-col">
                        <div class="flex items-center justify-center border-b-1 border-b-gray-200 border-b-solid py-1">
                          <el-text>体检费用</el-text>
                        </div>
                        <div class="flex-1">
                          <el-table :data="[]" stripe size="small" height="100%" :header-cell-style="{ backgroundColor: 'var(--el-fill-color-lighter)', color: 'var(--el-text-color-regular)' }">
                            <el-table-column prop="name" label="体检金额" align="center" />
                            <el-table-column prop="remaining" label="金额" align="center">
                              <el-table-column prop="remaining" label="自付" align="center" />
                              <el-table-column prop="max" label="统收" align="center" />
                            </el-table-column>
                          </el-table>
                        </div>
                      </div>
                    </el-scrollbar>
                  </el-splitter-panel>
                </el-splitter>
              </div>
            </el-card>
          </div>
        </el-splitter-panel>
      </el-splitter>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-splitter-bar__dragger-vertical::before) {
  height: 0;
}

.content-p-0 {
  :deep(.el-tabs__content) {
    padding: 0;
  }
}

.hide-input-icon {
  :deep(.el-input__prefix) {
    display: none;
  }
}

.tabs-bscj {
  :deep(.el-tabs__nav-scroll) {
    margin-right: 150px;
  }
}
</style>
