<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { login } from '~/api/common/auth'
import SliderVerify from '~/components/auth/SliderVerify.vue'
import { isDark, toggleDark, useAuth, useDict, useLoading } from '~/composables'
import { getConfig } from '~/utils'

definePage({
  meta: {
    title: '登录',
    public: true,
    hide: true,
  },
})

const route = useRoute()
const router = useRouter()
const { setToken, getUserInfo, setDept } = useAuth()
const { loading, withLoading } = useLoading()
const { clear: clearDict } = useDict()

// 状态
const formRef = useTemplateRef('formRef')
const sliderRef = useTemplateRef('sliderRef')
const rememberMe = ref(false)
const rememberValue = useStorage<string>('remember', null)
const appName = computed(() => getConfig().appName)

// 表单数据
const formData = reactive({
  loginName: '',
  password: '',
  slider: false,
})

// 验证规则
const formRules = {
  loginName: [
    { required: true, message: '请输入登录账号' },
  ],
  password: [
    { required: true, message: '请输入密码' },
  ],
  slider: [
    {
      validator: (_: any, value: boolean) => value === true,
      message: '请完成滑块验证',
    },
  ],
}

// 登录处理
async function handleLogin() {
  await formRef.value.validate()

  await withLoading(async () => {
    try {
      const token = await login({
        loginName: formData.loginName,
        password: formData.password,
        sliderData: sliderRef.value?.getData(),
      })

      // 设置登录token及科室
      setToken(token.data)
      const user = await getUserInfo()
      if (user.value?.deptPosts?.length) {
        setDept((user.value.deptPosts.find(item => item.main) || user.value.deptPosts[0]).deptId)
      }

      // 清理相关缓存数据
      clearDict()

      // 记住我
      if (rememberMe.value)
        rememberValue.value = formData.loginName
      else
        rememberValue.value = null

      ElMessage.success('登录成功')
      const redirectPath = route.query.redirect as string || '/'
      router.push(redirectPath.startsWith('/') ? redirectPath : '/')
    }
    catch {
      sliderRef.value?.reset()
    }
  })
}

// 变更时手动触发滑块验证
function handleSliderChange(_: boolean) {
  formRef.value?.validateField('slider')
}

onMounted(() => {
  // 如果存储中有用户名，则填充到表单中
  if (rememberValue.value) {
    formData.loginName = rememberValue.value
    rememberMe.value = true
  }
})
</script>

<template>
  <div class="h-screen w-full flex items-center justify-center overflow-hidden">
    <!-- 背景图片 -->
    <div class="absolute inset-0 bg-[url('/login-bg.png')] bg-cover bg-center dark:hue-rotate-180 dark:invert" />
    <!-- 背景装饰 -->
    <div class="absolute inset-0">
      <div class="absolute left-20 top-20 h-60 w-60 rounded-full bg-blue-400/20 blur-3xl" />
      <div class="absolute bottom-20 right-20 h-80 w-80 rounded-full bg-purple-400/20 blur-3xl" />
      <div class="absolute left-1/2 top-1/2 h-96 w-96 transform rounded-full bg-cyan-400/10 blur-3xl -translate-x-1/2 -translate-y-1/2" />
    </div>
    <div class="relative z-10 flex flex-col items-center justify-center px-4">
      <!-- 登录框 -->
      <el-config-provider size="large">
        <div class="w-310px border border-white/20 rounded-2xl bg-white/10 px-5 py-15 shadow-2xl backdrop-blur-xl md:w-[410px] dark:border-white/10 dark:bg-black/20 sm:px-25">
          <div class="flex select-none items-center justify-center">
            <!-- <img src="/logo.png" alt="Logo" class="mr-2 h-7 w-7 sm:h-9 sm:w-9"> -->
            <el-text class="text-2xl text-black sm:text-3xl dark:text-white">
              {{ appName }}
            </el-text>
          </div>
          <div class="mb-10 mt-2 text-center">
            <el-text class="text-gray-500 dark:text-gray-400">
              请登录您的账户以继续使用
            </el-text>
          </div>
          <!-- 登录表单 -->
          <el-form ref="formRef" :model="formData" :rules="formRules" @submit.prevent="handleLogin">
            <el-form-item prop="loginName">
              <el-input
                v-model="formData.loginName" tabindex="1" placeholder="请输入登录账号" clearable
                :autofocus="!rememberValue"
              >
                <template #prefix>
                  <icon icon="ep:user" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                v-model="formData.password" tabindex="2" type="password" placeholder="请输入密码" clearable
                show-password :autofocus="!!rememberValue"
              >
                <template #prefix>
                  <icon icon="ep:key" />
                </template>
              </el-input>
            </el-form-item>
            <!-- 滑块验证 -->
            <el-form-item prop="slider">
              <SliderVerify ref="sliderRef" v-model="formData.slider" @change="handleSliderChange" />
            </el-form-item>

            <div class="mb-4 flex items-center justify-between">
              <el-checkbox v-model="rememberMe">
                记住我
              </el-checkbox>
              <el-link v-if="false" type="primary" :underline="false">
                忘记密码?
              </el-link>
            </div>

            <el-button type="primary" class="w-full" :loading="loading" tabindex="3" native-type="submit">
              登录
            </el-button>
          </el-form>
        </div>
      </el-config-provider>
    </div>
    <!-- 暗黑模式切换 -->
    <el-button
      class="absolute right-4 top-4 !bg-transparent" style="-webkit-tap-highlight-color: transparent;" circle
      @click="toggleDark()"
    >
      <icon v-if="isDark" icon="line-md:moon-filled-to-sunny-filled-transition" />
      <icon v-else icon="line-md:moon-rising-twotone-loop" />
    </el-button>
  </div>
</template>
