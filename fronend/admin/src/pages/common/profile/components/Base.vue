<script setup lang="ts">
import * as api from '~/api/common/auth'
import { useLoading } from '~/composables'
import { tips } from '~/utils'

const { loading, withLoading } = useLoading()
const formRef = useTemplateRef('formRef')

// 用户基本信息表单
const form = reactive({
  loginName: '',
  userName: '',
  email: '',
  phone: '',
})

// 初始化数据
onMounted(async () => {
  const user = await api.getBaseInfo()
  Object.assign(form, user.data)
})

// 提交表单
async function handleSubmit() {
  await formRef.value.validate()
  await withLoading(async () => {
    await api.postBaseInfo(form)
    tips.success()
  })
}
</script>

<template>
  <el-config-provider size="large">
    <el-form ref="formRef" :model="form" label-width="90px" class="pt-6" @submit.prevent="handleSubmit">
      <el-form-item label="登录账号" class="w-100">
        <el-input v-model="form.loginName" disabled />
      </el-form-item>
      <el-form-item prop="userName" label="姓名" class="w-100" disabled>
        <el-input v-model="form.userName" disabled />
      </el-form-item>
      <el-form-item prop="email" label="邮箱" class="w-100">
        <el-input v-model="form.email" maxlength="63" />
      </el-form-item>
      <el-form-item prop="phone" label="手机" class="w-100">
        <el-input v-model="form.phone" maxlength="15" />
      </el-form-item>
      <el-form-item label=" " class="pt-4">
        <el-button type="primary" :loading="loading" native-type="submit" size="default">
          <template #icon>
            <icon icon="mdi:check" />
          </template>
          保存
        </el-button>
      </el-form-item>
    </el-form>
  </el-config-provider>
</template>
