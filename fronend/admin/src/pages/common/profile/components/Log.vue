<script setup lang="ts">
import * as api from '~/api/common/auth'
import { formatElCellDateTime } from '~/utils'
</script>

<template>
  <table-list :fetch-api="api.log" :pager="false">
    <template #left-actions>
      <el-text class="text-gray-400">
        仅显示最近30条记录
      </el-text>
    </template>
    <el-table-column prop="id" label="ID" align="center" width="100" />
    <el-table-column prop="loginTime" label="登录时间" align="center" width="200" :formatter="formatElCellDateTime" />
    <el-table-column prop="ip" label="登录IP" align="center" width="200" />
    <el-table-column prop="success" label="状态" align="center" width="180">
      <template #default="{ row }">
        <el-tag :type="row.success ? 'success' : 'danger'">
          {{ row.success ? '成功' : '失败' }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="error" label="提示信息" show-overflow-tooltip />
  </table-list>
</template>
