<script setup lang="ts">
import { formatElCellDateTime, noticeClient, resetForm, tips } from '~/utils'

definePage({
  meta: {
    title: '通知消息',
  },
})

// 状态及数据
const formRef = useTemplateRef('formRef')
const tableRef = useTemplateRef<any>('tableRef')
const canAction = computed(() => tableRef.value?.hasSelected && !tableRef.value?.loading)
const query = reactive({
  title: '',
  isRead: undefined,
})

// 操作消息
async function handleBy(ids: number[], fn: any) {
  if (ids && ids.length) {
    await tableRef.value.withLoading(async () => {
      await fn(ids)
      await tableRef.value.fetchData()
    })
  }
}

// 批量标记已读
async function handleMarkRead(ids: number[]) {
  await handleBy(ids, noticeClient.markRead)
}

// 批量删除
async function handleDelete(ids: number[]) {
  await handleBy(ids, noticeClient.delete)
  tips.success()
}

// 加载完成，将所有未读消息标记为已读
function handleDataLoaded(data: any) {
  if (data && data.items && data.items.length) {
    const unreadIds = data.items.filter((item: any) => !item.isRead).map((item: any) => item.id)
    if (unreadIds && unreadIds.length) {
      handleMarkRead(unreadIds)
    }
  }
}

onActivated(async () => {
  await tableRef.value.fetchData()
})
</script>

<template>
  <div class="p-4">
    <table-list ref="tableRef" :fetch-api="noticeClient.getPage" :query-params="query" @data-loaded="handleDataLoaded">
      <template #search>
        <el-form
          ref="formRef" :model="query" :inline="true" @submit.prevent="tableRef.search()"
          @reset="resetForm(formRef)"
        >
          <el-form-item prop="title" class="w-60">
            <el-input v-model="query.title" placeholder="请输入搜索关键词" clearable>
              <template #prefix>
                <icon icon="carbon:search" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="isRead" label="状态" class="w-36">
            <el-select v-model="query.isRead" clearable>
              <el-option label="未读" :value="false" />
              <el-option label="已读" :value="true" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit" :loading="tableRef?.loading">
              <template #icon>
                <icon icon="mdi:magnify" />
              </template>
              查询
            </el-button>
            <el-button native-type="reset">
              <template #icon>
                <icon icon="radix-icons:reset" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #left-actions>
        <el-button-group>
          <!-- <el-button title="设为已读" :disabled="!canAction" @click="handleMarkRead(tableRef.selected)">
            <template #icon>
              <icon icon="ic:twotone-mark-email-read" />
            </template>
设为已读
</el-button> -->
          <el-popconfirm title="确认要删除选中的项吗？" placement="bottom" @confirm="handleDelete(tableRef.selected)">
            <template #reference>
              <el-button title="删除" :disabled="!canAction">
                <template #icon>
                  <icon icon="ep:delete" />
                </template>
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </el-button-group>
      </template>
      <el-table-column type="selection" align="center" width="50" />
      <el-table-column prop="id" label="ID" align="center" width="100" />
      <el-table-column prop="title" label="标题" width="300" />
      <el-table-column prop="content" label="内容" show-overflow-tooltip />
      <el-table-column prop="createTime" label="时间" align="center" width="200" :formatter="formatElCellDateTime" />
      <el-table-column label="操作" align="center" width="200">
        <template #default="{ row }">
          <div class="space-x-2">
            <!-- <el-button v-if="!row.isRead" type="info" link @click="handleMarkRead([row.id])">
              设为已读
            </el-button> -->
            <el-popconfirm title="确认要删除该消息吗？" placement="left" @confirm="handleDelete([row.id])">
              <template #reference>
                <el-button type="warning" link>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </table-list>
  </div>
</template>
