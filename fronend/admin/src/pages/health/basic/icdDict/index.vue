<script setup lang="ts">
import * as api from '~/api/health/icdDict'
import { useDict } from '~/composables'
import { resetForm, tips } from '~/utils'

definePage({
  meta: {
    title: 'ICD编码',
    perm: 'health:icdDict',
    keepAlive: true,
    menu: true,
    order: 1,
  },
})

// 状态及数据
const { KEYS, getName } = useDict()
const queryFormRef = useTemplateRef('queryFormRef')
const tableRef = useTemplateRef<any>('tableRef')
const formRef = useTemplateRef('formRef')

const canAction = computed(() => tableRef.value?.hasSelected && !tableRef.value?.loading)
const getSexName = getName(KEYS.SEX_LIMIT)

// 表单
const query = reactive({
  type: 'name',
  key: '',
})
const initData = {
  icdNo: '',
  name: '',
  isContagion: false,
  isChronic: false,
  sex: 0,
  sort: 0,
  disabled: false,
}

// 验证规则
const rules = {
  name: [{ required: true, message: '请输入疾病名称' }],
}

// 操作
async function handleBy(ids: number[], fn: any) {
  if (ids && ids.length) {
    await tableRef.value.withLoading(async () => {
      await fn(ids)
      tips.success()
      await tableRef.value.fetchData()
    })
  }
}

// 删除
async function handleRemove(ids: number[]) {
  handleBy(ids, api.remove)
}

// 启用
async function handleEnable(ids: number[]) {
  handleBy(ids, api.enable)
}

// 禁用
async function handleDisable(ids: number[]) {
  handleBy(ids, api.disable)
}

// 导入
async function handleImport(file: File) {
  await api.importData(file)
}

async function handleImportSuccess() {
  tips.success('导入成功')
  await tableRef.value.fetchData()
}
</script>

<template>
  <div class="p-4">
    <table-list ref="tableRef" :fetch-api="api.list" :query-params="query" :export-api="$hasPerm('health:icdDict:export') ? api.exportData : undefined">
      <template #search>
        <el-form
          ref="queryFormRef" :model="query" :inline="true" @submit.prevent="tableRef.search()"
          @reset="resetForm(queryFormRef)"
        >
          <el-form-item prop="type" class="w-26 mr-1!">
            <el-select v-model="query.type">
              <el-option label="名称" value="name" />
              <el-option label="ICD编码" value="icdNo" />
            </el-select>
          </el-form-item>
          <el-form-item prop="key" class="w-60">
            <el-input v-model="query.key" placeholder="请输入搜索关键词" clearable>
              <template #prefix>
                <icon icon="carbon:search" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit" :loading="tableRef?.loading">
              <template #icon>
                <icon icon="mdi:magnify" />
              </template>
              查询
            </el-button>
            <el-button native-type="reset">
              <template #icon>
                <icon icon="radix-icons:reset" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #left-actions>
        <el-button v-if="$hasPerm('health:icdDict:add')" type="primary" @click="formRef.add()">
          <template #icon>
            <icon icon="ep:plus" />
          </template>
          新增
        </el-button>
        <el-button-group class="ml-2">
          <el-button v-if="$hasPerm('health:icdDict:enable')" title="启用" :disabled="!canAction" @click="handleEnable(tableRef.selected)">
            <template #icon>
              <icon icon="codicon:debug-start" />
            </template>
            启用
          </el-button>
          <el-popconfirm v-if="$hasPerm('health:icdDict:disable')" title="确认要禁用选中项吗？" placement="bottom" @confirm="handleDisable(tableRef.selected)">
            <template #reference>
              <el-button title="禁用选中" :disabled="!canAction">
                <template #icon>
                  <icon icon="ant-design:stop-twotone" />
                </template>
                禁用
              </el-button>
            </template>
          </el-popconfirm>
          <el-popconfirm v-if="$hasPerm('health:icdDict:delete')" title="确认要删除选中的项吗？" placement="bottom" @confirm="handleRemove(tableRef.selected)">
            <template #reference>
              <el-button title="删除" :disabled="!canAction">
                <template #icon>
                  <icon icon="ep:delete" />
                </template>
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </el-button-group>
      </template>
      <template #right-actions>
        <file-upload v-if="$hasPerm('health:icdDict:import')" accept=".xlsx,.xls" :max-size="5" :upload="handleImport" @success="handleImportSuccess">
          <template #default="{ loading, triggerUpload }">
            <el-button :loading="loading" round @click="triggerUpload">
              <template #icon>
                <icon icon="lets-icons:import" />
              </template>
              导入
            </el-button>
          </template>
        </file-upload>
      </template>
      <el-table-column type="selection" align="center" width="50" />
      <el-table-column prop="id" label="ID" align="center" width="100" />
      <el-table-column prop="icdNo" label="ICD编码" width="180" />
      <el-table-column prop="name" label="疾病名称" show-overflow-tooltip />
      <el-table-column prop="isContagion" label="传染病" align="center" width="120">
        <template #default="{ row }">
          {{ row.isContagion ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="isChronic" label="慢性病" align="center" width="120">
        <template #default="{ row }">
          {{ row.isChronic ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="sex" label="性别" align="center" width="120">
        <template #default="{ row }">
          {{ getSexName(row.sex) }}
        </template>
      </el-table-column>
      <el-table-column prop="sort" align="center" label="排序值" width="120" />
      <el-table-column prop="disabled" label="状态" width="120" align="center">
        <template #default="{ row }">
          <el-tag :type="row.disabled ? 'danger' : 'success'">
            {{ row.disabled ? '禁用' : '启用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template #default="{ row }">
          <div class="space-x-2">
            <el-button v-if="$hasPerm('health:icdDict:edit')" link @click="formRef.edit(row.id)">
              编辑
            </el-button>
            <el-popconfirm
              v-if="$hasPerm('health:icdDict:delete')" title="确认要删除该项吗？" placement="left"
              @confirm="handleRemove([row.id])"
            >
              <template #reference>
                <el-button type="warning" link>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </table-list>
  </div>

  <!-- 编辑对话框 -->
  <detail-form
    ref="formRef" class="p-6" title="ICD编码" :init-data="initData" :rules="rules"
    :get-api="api.getEdit" :add-api="api.add" :edit-api="api.postEdit" @success="tableRef.fetchData()"
  >
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="疾病名称" prop="name">
          <el-input v-model="formRef.formData.name" placeholder="请输入疾病名称" maxlength="127" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="ICD编码" prop="icdNo">
          <el-input v-model="formRef.formData.icdNo" placeholder="请输入ICD编码" maxlength="31" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="性别" prop="sex">
          <dict-select v-model="formRef.formData.sex" :dict-key="KEYS.SEX_LIMIT" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="传染病" prop="isContagion">
          <el-radio-group v-model="formRef.formData.isContagion">
            <el-radio-button label="是" :value="true" />
            <el-radio-button label="否" :value="false" />
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="慢性病" prop="isChronic">
          <el-radio-group v-model="formRef.formData.isChronic">
            <el-radio-button label="是" :value="true" />
            <el-radio-button label="否" :value="false" />
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formRef.formData.sort" :min="0" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="状态" prop="disabled">
          <el-radio-group v-model="formRef.formData.disabled">
            <el-radio-button label="启用" :value="false" />
            <el-radio-button label="禁用" :value="true" />
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
  </detail-form>
</template>
