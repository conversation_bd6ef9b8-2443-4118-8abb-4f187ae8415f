<script setup lang="ts">
import * as api from '~/api/health/docTemplate'
import { useDict, useLoading } from '~/composables'
import { resetForm, tips } from '~/utils'

definePage({
  meta: {
    title: '文书模板',
    perm: 'health:docTemplate',
    keepAlive: true,
    menu: true,
    order: 8,
  },
})

// 状态及数据
const queryFormRef = useTemplateRef('queryFormRef')
const tableRef = useTemplateRef<any>('tableRef')
const formRef = useTemplateRef<any>('formRef')
const { loading: previewLoading, withLoading: withPreviewLoading } = useLoading()
const { loading: schemaLoading, withLoading: withSchemaLoading } = useLoading()
const { KEYS } = useDict()
const schema = ref<any[]>([])
const schemaDialog = reactive({
  visible: false,
})
const previewDialog = reactive({
  visible: false,
  blob: null as Blob | null,
  filename: '',
})

const canAction = computed(() => tableRef.value?.hasSelected && !tableRef.value?.loading)

// 加载数据模型
async function loadSchema() {
  const code = formRef.value?.formData.code
  if (!code) {
    tips.error('请先选择模板编号')
    return
  }

  await withSchemaLoading(async () => {
    const model = await api.getSchema(code)
    schema.value = model.data || []
    schemaDialog.visible = true
  })
}

// 表单
const query = reactive({
  type: 'name',
  key: '',
  isSys: undefined,
})

const initData = {
  name: '',
  code: '',
  template: '',
  sort: 0,
  isSys: false,
  disabled: false,
}

// 验证规则
const rules = {
  name: [{ required: true, message: '请输入模板名称' }],
  code: [{ required: true, message: '请选择模板编号' }],
  template: [{ required: true, message: '请选择模板内容' }],
}

// 是否可选择
const selectable = (row: any) => row.isSys !== true

// 操作
async function handleBy(ids: number[], fn: any) {
  if (ids && ids.length) {
    await tableRef.value.withLoading(async () => {
      await fn(ids)
      tips.success()
      await tableRef.value.fetchData()
    })
  }
}

// 删除
async function handleRemove(ids: number[]) {
  handleBy(ids, api.remove)
}

// 启用
async function handleEnable(ids: number[]) {
  handleBy(ids, api.enable)
}

// 禁用
async function handleDisable(ids: number[]) {
  handleBy(ids, api.disable)
}

// 预览
async function handlePreview(html: string, desiredFilename = 'preview.pdf') {
  if (!html) {
    tips.error('模板内容为空，无法预览')
    return
  }

  // HTML格式验证
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/xml')
  const parsererror = doc.getElementsByTagName('parsererror')
  if (parsererror.length > 0) {
    tips.error(`模板解析错误: ${parsererror[0].textContent}`)
    return
  }

  // 调用API生成PDF
  const { blob, filename } = await api.pdfByHtml(html)
  previewDialog.blob = blob
  previewDialog.filename = desiredFilename || filename // 优先使用传入的文件名
  previewDialog.visible = true
}

// 列表预览
async function handleListPreview(row: any) {
  if (tableRef.value.loading)
    return
  await tableRef.value.withLoading(async () => {
    await handlePreview(row.template, `${row.name}.pdf`)
  })
}

// 表单预览
async function handleFormPreview() {
  await withPreviewLoading(async () => {
    const filename = formRef.value?.formData.name ? `${formRef.value.formData.name}.pdf` : 'preview.pdf'
    await handlePreview(formRef.value?.formData.template, filename)
  })
}

// 清理数据模型对话框
function closeSchemaDialog() {
  schemaDialog.visible = false
  schema.value = []
}

// 清理
watch(() => previewDialog.visible, (visible) => {
  if (!visible) {
    previewDialog.blob = null
    previewDialog.filename = ''
  }
})
</script>

<template>
  <div class="p-4">
    <table-list ref="tableRef" :fetch-api="api.list" :query-params="query">
      <template #search>
        <el-form
          ref="queryFormRef" :model="query" :inline="true" @submit.prevent="tableRef.search()"
          @reset="resetForm(queryFormRef)"
        >
          <el-form-item prop="type" class="w-26 mr-1!">
            <el-select v-model="query.type">
              <el-option label="模板名称" value="name" />
              <el-option label="模板编号" value="code" />
            </el-select>
          </el-form-item>
          <el-form-item prop="key" class="w-60">
            <el-input v-model="query.key" placeholder="请输入搜索关键词" clearable>
              <template #prefix>
                <icon icon="carbon:search" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="isSys" label="类型" class="w-36">
            <el-select v-model="query.isSys" clearable>
              <el-option label="系统默认" :value="true" />
              <el-option label="用户定义" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit" :loading="tableRef?.loading">
              <template #icon>
                <icon icon="mdi:magnify" />
              </template>
              查询
            </el-button>
            <el-button native-type="reset">
              <template #icon>
                <icon icon="radix-icons:reset" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #left-actions>
        <el-button v-if="$hasPerm('health:docTemplate:add')" type="primary" @click="formRef.add()">
          <template #icon>
            <icon icon="ep:plus" />
          </template>
          新增
        </el-button>
        <el-button-group class="ml-2">
          <el-button v-if="$hasPerm('health:docTemplate:enable')" title="启用" :disabled="!canAction" @click="handleEnable(tableRef.selected)">
            <template #icon>
              <icon icon="codicon:debug-start" />
            </template>
            启用
          </el-button>
          <el-popconfirm v-if="$hasPerm('health:docTemplate:disable')" title="确认要禁用选中项吗？" placement="bottom" @confirm="handleDisable(tableRef.selected)">
            <template #reference>
              <el-button title="禁用选中" :disabled="!canAction">
                <template #icon>
                  <icon icon="ant-design:stop-twotone" />
                </template>
                禁用
              </el-button>
            </template>
          </el-popconfirm>
          <el-popconfirm v-if="$hasPerm('health:docTemplate:delete')" title="确认要删除选中的项吗？" placement="bottom" @confirm="handleRemove(tableRef.selected)">
            <template #reference>
              <el-button title="删除" :disabled="!canAction">
                <template #icon>
                  <icon icon="ep:delete" />
                </template>
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </el-button-group>
      </template>
      <el-table-column type="selection" align="center" width="50" :selectable="selectable" />
      <el-table-column prop="id" label="ID" align="center" width="100" />
      <el-table-column prop="name" label="模板名称" show-overflow-tooltip />
      <el-table-column prop="code" label="模板编码" width="200" />
      <el-table-column prop="isSys" label="类型" align="center" width="200">
        <template #default="{ row }">
          <el-tag :type="row.isSys ? 'info' : 'success'">
            {{ row.isSys ? '系统默认' : '用户定义' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="sort" align="center" label="排序值" width="150" />
      <el-table-column prop="disabled" label="状态" width="150" align="center">
        <template #default="{ row }">
          <el-tag :type="row.disabled ? 'danger' : 'success'">
            {{ row.disabled ? '禁用' : '启用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template #default="{ row }">
          <div class="space-x-2">
            <el-button v-if="$hasPerm('health:docTemplate:edit')" link @click="formRef.edit(row.id)">
              编辑
            </el-button>
            <el-button link @click="handleListPreview(row)">
              预览
            </el-button>
            <el-popconfirm
              v-if="$hasPerm('health:docTemplate:delete') && row.isSys !== true" title="确认要删除该项吗？" placement="left"
              @confirm="handleRemove([row.id])"
            >
              <template #reference>
                <el-button type="warning" link>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </table-list>
  </div>

  <!-- 编辑对话框 -->
  <detail-form
    ref="formRef" class="p-6" title="文书模板" :init-data="initData" :rules="rules" width="800px"
    :get-api="api.getEdit" :add-api="api.add" :edit-api="api.postEdit" @success="tableRef.fetchData()"
  >
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="formRef.formData.name" :disabled="formRef.formData.isSys" placeholder="请输入模板名称" maxlength="63" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="模板编号" prop="code">
          <dict-select v-model="formRef.formData.code" :disabled="formRef.formData.isSys" :dict-key="KEYS.DOC_CODES" placeholder="请选择模板编号" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="模板内容" prop="template">
          <el-input v-model="formRef.formData.template" :disabled="formRef.formData.isSys" type="textarea" :rows="10" placeholder="请输入模板内容" />
          <div class="mt-2 w-full flex justify-between">
            <el-button :loading="schemaLoading" @click="loadSchema">
              数据模型
            </el-button>
            <el-button :loading="previewLoading" @click="handleFormPreview">
              预览
            </el-button>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="mt-2">
      <el-col :span="12">
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formRef.formData.sort" :min="0" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="状态" prop="disabled">
          <el-radio-group v-model="formRef.formData.disabled" :disabled="formRef.formData.isSys">
            <el-radio-button label="启用" :value="false" />
            <el-radio-button label="禁用" :value="true" />
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
  </detail-form>

  <!-- 预览 -->
  <el-dialog v-model="previewDialog.visible" title="模板预览" width="80%" draggable>
    <div class="h-70vh">
      <doc-view v-if="previewDialog.blob" :blob="previewDialog.blob" :filename="previewDialog.filename" />
    </div>
  </el-dialog>

  <!-- 数据模型 -->
  <el-dialog v-model="schemaDialog.visible" title="数据模型" width="600px" draggable @close="closeSchemaDialog">
    <div class="max-h-70vh overflow-auto">
      <el-tree
        v-if="schema.length"
        :data="schema"
        :props="{ children: 'children', label: 'label' }"
        :default-expand-all="true"
        :expand-on-click-node="false"
        class="w-full"
      >
        <template #default="{ data: nodeData }">
          <div class="w-full flex items-center justify-between pr-4">
            <div class="flex flex-1 items-center space-x-3">
              <el-tag size="small">
                {{ nodeData.name }}
              </el-tag>
              <span class="truncate text-sm text-gray-600">{{ nodeData.description }}</span>
            </div>
            <span class="opacity-60">
              {{ nodeData.type }}
            </span>
          </div>
        </template>
      </el-tree>
      <el-empty v-else description="暂无数据模型" />
    </div>
  </el-dialog>
</template>
