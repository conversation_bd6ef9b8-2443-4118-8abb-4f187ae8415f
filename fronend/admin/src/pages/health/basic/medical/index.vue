<script setup lang="ts">
import * as api from '~/api/health/medical'
import { useDict, useLoading } from '~/composables'
import { resetForm, tips } from '~/utils'

definePage({
  meta: {
    title: '病史管理',
    perm: 'health:medical',
    keepAlive: true,
    menu: true,
    order: 2,
  },
})

// 状态及数据
const { KEYS, getName } = useDict()
const queryFormRef = useTemplateRef('queryFormRef')
const tableRef = useTemplateRef<any>('tableRef')
const formRef = useTemplateRef('formRef')
const valueFormRef = useTemplateRef('valueFormRef')
const show = ref()
const valueList = ref([])
const dialogVisible = ref(false)
const selected = ref([])
const { loading: valueLoading, withLoading: valueWithLoading } = useLoading()
const { loading: exportLoading, withLoading: exportWithLoading } = useLoading()

const canAction = computed(() => tableRef.value?.hasSelected && !tableRef.value?.loading)
const valueCanAction = computed(() => selected.value.length > 0 && !valueLoading.value)
const getSexLimitName = getName(KEYS.SEX_LIMIT)
const getAgeLimitName = getName(KEYS.AGE_LIMIT)

// 表单
const query = reactive({
  type: 'name',
  key: '',
})
const initData = {
  name: '',
  sex: 0,
  age: 0,
  sort: 0,
  disabled: false,
}
const initValueData = {
  typeId: 0,
  name: '',
  icdCode: '',
  sex: 0,
  age: 0,
  sort: 0,
  disabled: false,
}

// 验证规则
const rules = {
  name: [{ required: true, message: '请输入类别名称' }],
}
const valueRules = {
  name: [{ required: true, message: '请输入病史名称' }],
}

// 加载值
async function loadValue() {
  if (show.value && show.value.id > 0) {
    await valueWithLoading(async () => {
      const res = await api.listValue(show.value.id)
      valueList.value = res.data
    })
  }
  else {
    valueList.value = []
  }
}

// 删除
async function handleRemove(ids: number[]) {
  if (ids && ids.length) {
    await tableRef.value.withLoading(async () => {
      await api.remove(ids)
      tips.success()
      await tableRef.value.fetchData()
    })
  }
}

// 显示值
async function handleShowValues(item: any) {
  show.value = item
  dialogVisible.value = true
  await loadValue()
}

// 值选中
function handleValueSelectionChange(val: any) {
  selected.value = val.map((item: any) => item.id)
}

// 操作值
async function handleValueBy(ids: number[], fn: any) {
  await valueWithLoading(async () => {
    await fn(ids)
    tips.success()
    await loadValue()
  })
}

// 启用值
async function handleEnableValue(ids: number[]) {
  await handleValueBy(ids, api.enableValue)
}

// 禁用值
async function handleDisableValue(ids: number[]) {
  await handleValueBy(ids, api.disableValue)
}

// 删除值
async function handleRemoveValue(ids: number[]) {
  await handleValueBy(ids, api.removeValue)
}

// 导出值
function handleExportValue() {
  if (!show.value?.id)
    return

  exportWithLoading(async () => {
    await api.exportValue(show.value.id)
  })
}

// 导入值
async function handleImportValue(file: File) {
  await api.importValue(show.value.id, file)
}

async function handleImportSuccess() {
  tips.success('导入成功')
  await loadValue()
}
</script>

<template>
  <div class="p-4">
    <table-list ref="tableRef" :fetch-api="api.list" :query-params="query">
      <template #search>
        <el-form
          ref="queryFormRef" :model="query" :inline="true" @submit.prevent="tableRef.search()"
          @reset="resetForm(queryFormRef)"
        >
          <!-- <el-form-item prop="type" class="w-26 mr-1!">
            <el-select v-model="query.type">
              <el-option label="名称" value="name" />
            </el-select>
          </el-form-item> -->
          <el-form-item prop="key" class="w-60">
            <el-input v-model="query.key" placeholder="请输入搜索关键词" clearable>
              <template #prefix>
                <icon icon="carbon:search" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit" :loading="tableRef?.loading">
              <template #icon>
                <icon icon="mdi:magnify" />
              </template>
              查询
            </el-button>
            <el-button native-type="reset">
              <template #icon>
                <icon icon="radix-icons:reset" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #left-actions>
        <el-button v-if="$hasPerm('health:medical:add')" type="primary" @click="formRef.add()">
          <template #icon>
            <icon icon="ep:plus" />
          </template>
          新增
        </el-button>
        <el-button-group v-if="$hasPerm('health:medical:delete')">
          <el-popconfirm title="确认要删除选中的项吗？" placement="bottom" @confirm="handleRemove(tableRef.selected)">
            <template #reference>
              <el-button title="删除" :disabled="!canAction">
                <template #icon>
                  <icon icon="ep:delete" />
                </template>
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </el-button-group>
      </template>
      <el-table-column type="selection" align="center" width="50" />
      <el-table-column prop="id" label="ID" align="center" width="100" />
      <el-table-column prop="name" label="类别名称" show-overflow-tooltip>
        <template #default="{ row }">
          <el-button link @click="handleShowValues(row)">
            {{ row.name }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="sex" label="性别" align="center" width="120">
        <template #default="{ row }">
          {{ getSexLimitName(row.sex) }}
        </template>
      </el-table-column>
      <el-table-column prop="age" label="年龄阶段" align="center" width="120">
        <template #default="{ row }">
          {{ getAgeLimitName(row.age) }}
        </template>
      </el-table-column>
      <el-table-column prop="sort" align="center" label="排序值" width="120" />
      <el-table-column prop="disabled" label="状态" width="120" align="center">
        <template #default="{ row }">
          <el-tag :type="row.disabled ? 'danger' : 'success'">
            {{ row.disabled ? '禁用' : '启用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template #default="{ row }">
          <div class="space-x-2">
            <el-button link @click="handleShowValues(row)">
              病史项
            </el-button>
            <el-button v-if="$hasPerm('health:medical:edit')" link @click="formRef.edit(row.id)">
              编辑
            </el-button>
            <el-popconfirm
              v-if="$hasPerm('health:medical:delete')" title="确认要删除该项吗？" placement="left"
              @confirm="handleRemove([row.id])"
            >
              <template #reference>
                <el-button type="warning" link>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </table-list>
  </div>

  <!-- 编辑对话框 -->
  <detail-form
    ref="formRef" class="p-6" title="病史类别" :init-data="initData" :rules="rules" width="650px"
    :get-api="api.getEdit" :add-api="api.add" :edit-api="api.postEdit" @success="tableRef.fetchData()"
  >
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="类别名称" prop="name">
          <el-input v-model="formRef.formData.name" placeholder="请输入类别名称" maxlength="31" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="性别" prop="sex">
          <dict-select v-model="formRef.formData.sex" :dict-key="KEYS.SEX_LIMIT" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="年龄阶段" prop="age">
          <dict-select v-model="formRef.formData.age" :dict-key="KEYS.AGE_LIMIT" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formRef.formData.sort" :min="0" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="状态" prop="disabled">
          <el-radio-group v-model="formRef.formData.disabled">
            <el-radio-button label="启用" :value="false" />
            <el-radio-button label="禁用" :value="true" />
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
  </detail-form>

  <!-- 病史值对话框 -->
  <el-dialog v-model="dialogVisible" :title="show?.name" width="1000px" draggable>
    <div class="px-2 py-5">
      <div class="mb-4 flex items-center justify-between">
        <div>
          <el-button
            v-if="$hasPerm('health:medical:add')" type="primary"
            @click="valueFormRef.add({ typeId: show?.id })"
          >
            <template #icon>
              <icon icon="ep:plus" />
            </template>
            新增病史项
          </el-button>
          <el-button-group class="ml-2">
            <el-button v-if="$hasPerm('health:medical:valueEnable')" title="启用" :disabled="!valueCanAction" @click="handleEnableValue(selected)">
              <template #icon>
                <icon icon="codicon:debug-start" />
              </template>
              启用
            </el-button>
            <el-popconfirm v-if="$hasPerm('health:medical:valueDisable')" title="确认要禁用选中项吗？" placement="bottom" @confirm="handleDisableValue(selected)">
              <template #reference>
                <el-button title="禁用选中" :disabled="!valueCanAction">
                  <template #icon>
                    <icon icon="ant-design:stop-twotone" />
                  </template>
                  禁用
                </el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm
              v-if="$hasPerm('health:medical:delete')" title="确认要删除选中项吗？"
              @confirm="handleRemoveValue(selected)"
            >
              <template #reference>
                <el-button :disabled="!valueCanAction">
                  <template #icon>
                    <icon icon="ep:delete" />
                  </template>
                  删除选中
                </el-button>
              </template>
            </el-popconfirm>
          </el-button-group>
        </div>
        <div>
          <el-button-group>
            <file-upload
              v-if="$hasPerm('health:medical:import')" accept=".xlsx,.xls" :max-size="5" :upload="handleImportValue" @success="handleImportSuccess"
            >
              <template #default="{ loading, triggerUpload }">
                <el-button round :loading="loading" @click="triggerUpload">
                  <template #icon>
                    <icon icon="lets-icons:import" />
                  </template>
                  导入
                </el-button>
              </template>
            </file-upload>
            <el-button v-if="$hasPerm('health:medical:export')" round :loading="exportLoading" @click="handleExportValue">
              <template #icon>
                <icon icon="material-symbols:upload-rounded" />
              </template>
              导出
            </el-button>
          </el-button-group>
        </div>
      </div>
      <el-table v-loading="valueLoading" :data="valueList" @selection-change="handleValueSelectionChange">
        <el-table-column type="selection" width="50" />
        <el-table-column prop="name" label="名称" show-overflow-tooltip />
        <el-table-column prop="icdCode" label="ICD编码" show-overflow-tooltip />
        <el-table-column prop="sex" label="性别" align="center" width="120">
          <template #default="{ row }">
            {{ getSexLimitName(row.sex) }}
          </template>
        </el-table-column>
        <el-table-column prop="age" label="年龄阶段" align="center" width="120">
          <template #default="{ row }">
            {{ getAgeLimitName(row.age) }}
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="100" align="center" />
        <el-table-column prop="disabled" label="状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.disabled ? 'danger' : 'success'">
              {{ row.disabled ? '禁用' : '启用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template #default="{ row }">
            <el-button v-if="$hasPerm('health:medical:edit')" link @click="valueFormRef.edit(row.id)">
              编辑
            </el-button>
            <el-popconfirm
              v-if="$hasPerm('health:medical:delete')" title="确认要删除该项吗？"
              @confirm="handleRemoveValue([row.id])"
            >
              <template #reference>
                <el-button type="warning" link>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <el-button @click="dialogVisible = false">
        关闭
      </el-button>
    </template>
  </el-dialog>

  <!-- 病史值编辑对话框 -->
  <detail-form
    ref="valueFormRef" class="p-6" title="病史项" :init-data="initValueData" :rules="valueRules" width="650px"
    :get-api="api.getValueEdit" :add-api="api.addValue" :edit-api="api.postValueEdit" @success="loadValue"
  >
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="名称" prop="name">
          <el-input v-model="valueFormRef.formData.name" placeholder="请输入值名称" maxlength="127" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="ICD编码" prop="icdCode">
          <el-input v-model="valueFormRef.formData.icdCode" placeholder="请输入ICD编码" maxlength="31" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="性别" prop="sex">
          <dict-select v-model="valueFormRef.formData.sex" :dict-key="KEYS.SEX_LIMIT" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="年龄阶段" prop="age">
          <dict-select v-model="valueFormRef.formData.age" :dict-key="KEYS.AGE_LIMIT" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="valueFormRef.formData.sort" :min="0" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="状态" prop="disabled">
          <el-radio-group v-model="valueFormRef.formData.disabled">
            <el-radio-button label="启用" :value="false" />
            <el-radio-button label="禁用" :value="true" />
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
  </detail-form>
</template>
