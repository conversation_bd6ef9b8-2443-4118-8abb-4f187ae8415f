<script setup lang="ts">
import * as api from '~/api/health/projectDict'
import { useDict } from '~/composables'
import { resetForm, tips } from '~/utils'

definePage({
  meta: {
    title: '项目字典',
    perm: 'health:projectDict',
    keepAlive: true,
    menu: true,
    order: 1,
  },
})

// 状态及数据
const queryFormRef = useTemplateRef('queryFormRef')
const tableRef = useTemplateRef<any>('tableRef')
const formRef = useTemplateRef('formRef')
const { KEYS, getName } = useDict()

const canAction = computed(() => tableRef.value?.hasSelected && !tableRef.value?.loading)
const getTypeName = getName(KEYS.DICT.PROJECT_TYPE)

// 表单
const query = reactive({
  type: 'name',
  key: '',
  typeId: undefined,
})

const initData = {
  name: '',
  englishName: '',
  printName: '',
  code: '',
  valueType: 'C',
  valueUnit: '',
  bodyPartIds: [],
  typeId: undefined,
  sort: 0,
  remark: '',
  disabled: false,
}

// 验证规则
const rules = {
  name: [{ required: true, message: '请输入项目名称' }],
  typeId: [{ required: true, message: '请选择项目类型' }],
}

// 操作
async function handleBy(ids: number[], fn: any) {
  if (ids && ids.length) {
    await tableRef.value.withLoading(async () => {
      await fn(ids)
      tips.success()
      await tableRef.value.fetchData()
    })
  }
}

// 删除
async function handleRemove(ids: number[]) {
  handleBy(ids, api.remove)
}

// 启用
async function handleEnable(ids: number[]) {
  handleBy(ids, api.enable)
}

// 禁用
async function handleDisable(ids: number[]) {
  handleBy(ids, api.disable)
}

// 导入
async function handleImport(file: File) {
  await api.importData(file)
}

async function handleImportSuccess() {
  tips.success('导入成功')
  await tableRef.value.fetchData()
}
</script>

<template>
  <div class="p-4">
    <table-list ref="tableRef" :fetch-api="api.list" :query-params="query" :export-api="$hasPerm('health:projectDict:export') ? api.exportData : undefined">
      <template #search>
        <el-form
          ref="queryFormRef" :model="query" :inline="true" @submit.prevent="tableRef.search()"
          @reset="resetForm(queryFormRef)"
        >
          <el-form-item prop="type" class="w-26 mr-1!">
            <el-select v-model="query.type">
              <el-option label="项目名称" value="name" />
              <el-option label="接口编码" value="code" />
            </el-select>
          </el-form-item>
          <el-form-item prop="key" class="w-60">
            <el-input v-model="query.key" placeholder="请输入搜索关键词" clearable>
              <template #prefix>
                <icon icon="carbon:search" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="项目类型" prop="typeId">
            <dict-select v-model="query.typeId" :dict-key="KEYS.DICT.PROJECT_TYPE" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit" :loading="tableRef?.loading">
              <template #icon>
                <icon icon="mdi:magnify" />
              </template>
              查询
            </el-button>
            <el-button native-type="reset">
              <template #icon>
                <icon icon="radix-icons:reset" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #left-actions>
        <el-button v-if="$hasPerm('health:projectDict:add')" type="primary" @click="formRef.add()">
          <template #icon>
            <icon icon="ep:plus" />
          </template>
          新增
        </el-button>
        <el-button-group class="ml-2">
          <el-button v-if="$hasPerm('health:projectDict:enable')" title="启用" :disabled="!canAction" @click="handleEnable(tableRef.selected)">
            <template #icon>
              <icon icon="codicon:debug-start" />
            </template>
            启用
          </el-button>
          <el-popconfirm v-if="$hasPerm('health:projectDict:disable')" title="确认要禁用选中项吗？" placement="bottom" @confirm="handleDisable(tableRef.selected)">
            <template #reference>
              <el-button title="禁用选中" :disabled="!canAction">
                <template #icon>
                  <icon icon="ant-design:stop-twotone" />
                </template>
                禁用
              </el-button>
            </template>
          </el-popconfirm>
          <el-popconfirm v-if="$hasPerm('health:projectDict:delete')" title="确认要删除选中的项吗？" placement="bottom" @confirm="handleRemove(tableRef.selected)">
            <template #reference>
              <el-button title="删除" :disabled="!canAction">
                <template #icon>
                  <icon icon="ep:delete" />
                </template>
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </el-button-group>
      </template>
      <template #right-actions>
        <file-upload v-if="$hasPerm('health:projectDict:import')" accept=".xlsx,.xls" :max-size="5" :upload="handleImport" @success="handleImportSuccess">
          <template #default="{ loading, triggerUpload }">
            <el-button :loading="loading" round @click="triggerUpload">
              <template #icon>
                <icon icon="lets-icons:import" />
              </template>
              导入
            </el-button>
          </template>
        </file-upload>
      </template>
      <el-table-column type="selection" align="center" width="50" />
      <el-table-column prop="id" label="ID" align="center" width="80" />
      <el-table-column prop="name" label="项目名称" show-overflow-tooltip />
      <el-table-column prop="typeId" label="项目类型" width="120">
        <template #default="{ row }">
          {{ getTypeName(row.typeId) }}
        </template>
      </el-table-column>
      <el-table-column prop="valueType" label="值类型" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.valueType === 'C' ? 'primary' : 'success'" size="small">
            {{ row.valueType === 'C' ? '字符' : '数值' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="valueUnit" label="值单位" width="100" />
      <el-table-column prop="sort" align="center" label="排序值" width="120" />
      <el-table-column prop="disabled" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.disabled ? 'danger' : 'success'" size="small">
            {{ row.disabled ? '禁用' : '启用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" show-overflow-tooltip />
      <el-table-column label="操作" align="center" width="150">
        <template #default="{ row }">
          <div class="space-x-2">
            <el-button v-if="$hasPerm('health:projectDict:edit')" link @click="formRef.edit(row.id)">
              编辑
            </el-button>
            <el-popconfirm
              v-if="$hasPerm('health:projectDict:delete')" title="确认要删除该项吗？" placement="left"
              @confirm="handleRemove([row.id])"
            >
              <template #reference>
                <el-button type="warning" link>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </table-list>
  </div>

  <!-- 编辑对话框 -->
  <detail-form
    ref="formRef" class="p-6" title="项目字典" :init-data="initData" :rules="rules" width="800px"
    :get-api="api.getEdit" :add-api="api.add" :edit-api="api.postEdit" @success="tableRef.fetchData()"
  >
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="formRef.formData.name" placeholder="请输入项目名称" maxlength="63" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="项目类型" prop="typeId">
          <dict-select v-model="formRef.formData.typeId" :dict-key="KEYS.DICT.PROJECT_TYPE" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="英文名称" prop="englishName">
          <el-input v-model="formRef.formData.englishName" placeholder="请输入英文名称" maxlength="63" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="打印名称" prop="printName">
          <el-input v-model="formRef.formData.printName" placeholder="请输入打印名称" maxlength="63" clearable />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="对应身体部位" prop="bodyPartIds">
          <dict-select v-model="formRef.formData.bodyPartIds" :dict-key="KEYS.DICT.BODY_PART" multiple clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="接口编码" prop="code">
          <el-input v-model="formRef.formData.code" placeholder="请输入接口编码" maxlength="31" clearable />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="值类型" prop="valueType">
          <el-radio-group v-model="formRef.formData.valueType">
            <el-radio-button label="C" value="C">
              字符
            </el-radio-button>
            <el-radio-button label="N" value="N">
              数值
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="值单位" prop="valueUnit">
          <el-input v-model="formRef.formData.valueUnit" placeholder="请输入值单位" maxlength="31" clearable />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formRef.formData.sort" :min="0" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="状态" prop="disabled">
          <el-radio-group v-model="formRef.formData.disabled">
            <el-radio-button label="启用" :value="false" />
            <el-radio-button label="禁用" :value="true" />
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formRef.formData.remark" type="textarea" placeholder="请输入备注" maxlength="511" :rows="3" clearable />
        </el-form-item>
      </el-col>
    </el-row>
  </detail-form>
</template>
